<template>
  <div class="home-view">
    <standalone-world-map :path-data="pathData" :height="'100vh'" :animation-enabled="true" :show-title="false"
      :colors="mapColors" @map-ready="onMapReady" />
  </div>
</template>

<script>
import StandaloneWorldMap from './StandaloneWorldMap.vue'

export default {
  name: 'HomeView',
  components: {
    StandaloneWorldMap
  },
  data() {
    return {
      mapColors: {
        land: 'rgb(30, 65, 51)',
        borders: 'rgb(76, 159, 123)',
        ocean: 'rgb(18, 46, 44)',
        pathLine: '#ffffff',
        sourcePoint: '#ffffff',
        targetPoint: '#ffffff'
      },
      pathData: [
        {
          coords: [[39.9042, 116.4074], [40.7128, -74.0060]], // Beijing to New York
          value: 200,
          name: 'Beijing to New York'
        },
        {
          coords: [[51.5074, -0.1278], [35.6762, 139.6503]], // London to Tokyo
          value: 180,
          name: 'London to Tokyo'
        },
        {
          coords: [[-33.8688, 151.2093], [1.3521, 103.8198]], // Sydney to Singapore
          value: 150,
          name: 'Sydney to Singapore'
        },
        {
          coords: [[55.7558, 37.6173], [28.7041, 77.1025]], // Moscow to Delhi
          value: 120,
          name: 'Moscow to Delhi'
        },
        {
          coords: [[48.8566, 2.3522], [34.0522, -118.2437]], // Paris to Los Angeles
          value: 160,
          name: 'Paris to Los Angeles'
        },
        {
          coords: [[13.4050, 52.5200], [-22.9068, -43.1729]], // Berlin to Rio
          value: 140,
          name: 'Berlin to Rio de Janeiro'
        },
        {
          coords: [[31.2304, 121.4737], [37.7749, -122.4194]], // Shanghai to San Francisco
          value: 190,
          name: 'Shanghai to San Francisco'
        },
        {
          coords: [[25.2048, 55.2708], [59.9311, 30.3609]], // Dubai to St Petersburg
          value: 110,
          name: 'Dubai to St Petersburg'
        }
      ]
    }
  },
  methods: {
    onMapReady(map) {
      console.log('Home page world map is ready:', map)
      // Map is ready and configured with no zoom/pan interactions
    }
  }
}
</script>

<style scoped>
.home-view {
  width: 100vw;
  height: 100vh;
  background-color: #0F2E2C;
  position: relative;
  overflow: hidden;
}

/* Add subtle tech grid overlay */
.home-view::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(76, 159, 123, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(76, 159, 123, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  pointer-events: none;
  z-index: 1;
}

/* Add subtle radial gradient for depth */
.home-view::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 50% 50%, rgba(0, 255, 204, 0.02) 0%, rgba(0, 0, 0, 0) 70%);
  pointer-events: none;
  z-index: 2;
}
</style>
