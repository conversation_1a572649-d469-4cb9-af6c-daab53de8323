<template>
  <div class="dashboard">
    <header class="dashboard-header">中老特色农作物种质资源联合实验室种质资源数字信息管理平台</header>
    <div class="dashboard-content">
      <left-panel />
      <map-container />
      <right-panel />
    </div>
    <bottom-nav />
  </div>
</template>

<script>
import LeftPanel from './LeftPanel.vue'
import MapContainer from './MapContainer.vue'
import RightPanel from './RightPanel.vue'
import BottomNav from './BottomNav.vue'

export default {
  name: 'HomeView',
  components: {
    LeftPanel,
    MapContainer,
    RightPanel,
    BottomNav
  }
}
</script>

<style>
/* Styles are already defined in App.vue */
</style>
