<template>
  <div class="map-container">
    <div class="map-title">
      <span class="active-title">世界分布</span>
      <span class="divider">|</span>
      <span class="inactive-title">区域详情</span>
    </div>
    <div id="leaflet-map" class="map-chart"></div>
    <!-- Removed zoom controls as per requirement -->
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'MapContainer',
  data() {
    return {
      map: null,
      pathLines: [],
      sourceMarkers: [],
      targetMarkers: [],
      leafletLoaded: false
    }
  },
  computed: {
    ...mapGetters('mapData', [
      'getPathData',
      'getSelectedRegions',
      'getZoomLevel',
      'getCenter',
      'getMapConfig'
    ])
  },
  mounted() {
    this.loadLeaflet();
    this.loadPathData();

    // Add a MutationObserver to ensure path lines have no fill
    this.setupSVGObserver();
  },
  beforeDestroy() {
    if (this.map) {
      this.map.remove();
    }

    // Clean up the SVG observer
    if (this.svgObserver) {
      this.svgObserver.disconnect();
    }
  },
  methods: {
    ...mapActions('mapData', [
      'loadPathData',
      'updateSelectedRegions',
      'updateZoomLevel',
      'updateCenter'
    ]),

    // Setup an observer to ensure SVG paths have no fill
    setupSVGObserver() {
      // Create a MutationObserver to watch for changes to the DOM
      const observer = new MutationObserver((mutations) => {
        // For each mutation, check if SVG paths were added
        mutations.forEach(() => {
          // Find all SVG paths in the connection-line class and overlay pane
          const pathElements = document.querySelectorAll(
            '.connection-line path, .leaflet-overlay-pane path, .pure-line-path path'
          );

          // Set fill to none for each path
          pathElements.forEach(path => {
            path.setAttribute('fill', 'none');
            path.setAttribute('fill-opacity', '0');

            // If this is a connection line, ensure it has the right stroke properties
            if (path.closest('.connection-line') || path.closest('.pure-line-path')) {
              path.setAttribute('stroke', '#ffffff');
              path.setAttribute('stroke-opacity', '0.8');
              path.setAttribute('vector-effect', 'non-scaling-stroke');
            }
          });
        });
      });

      // Start observing the document with the configured parameters
      observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'd']
      });

      // Store the observer for cleanup
      this.svgObserver = observer;

      // Also run once immediately to catch any existing paths
      setTimeout(() => {
        const pathElements = document.querySelectorAll(
          '.connection-line path, .leaflet-overlay-pane path, .pure-line-path path'
        );

        pathElements.forEach(path => {
          path.setAttribute('fill', 'none');
          path.setAttribute('fill-opacity', '0');
        });
      }, 500);
    },
    loadLeaflet() {
      // Dynamically load Leaflet CSS
      const leafletCss = document.createElement('link');
      leafletCss.rel = 'stylesheet';
      leafletCss.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
      document.head.appendChild(leafletCss);

      // Dynamically load Leaflet JS
      const leafletScript = document.createElement('script');
      leafletScript.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
      leafletScript.onload = () => {
        this.leafletLoaded = true;
        this.initMap();
      };
      document.head.appendChild(leafletScript);
    },
    initMap() {
      if (!this.leafletLoaded || !window.L) return;

      // Initialize the map with fixed zoom and disabled interactions
      this.map = L.map('leaflet-map', {
        center: [20, 0],
        zoom: 2,
        zoomControl: false,
        attributionControl: false,
        dragging: false,      // Disable panning
        touchZoom: false,     // Disable touch zoom
        doubleClickZoom: false, // Disable double-click zoom
        scrollWheelZoom: false, // Disable scroll wheel zoom
        boxZoom: false,       // Disable box zoom
        keyboard: false,      // Disable keyboard navigation
        tap: false            // Disable tap handler
      });

      // Add the tile layer with custom styling
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; OpenStreetMap contributors'
      }).addTo(this.map);

      // Apply custom styling to map tiles and background
      const styleElement = document.createElement('style');
      styleElement.textContent = `
        /* Ocean/background color */
        #leaflet-map {
          background-color: rgb(18, 46, 44) !important;
        }
        .leaflet-container {
          background-color: rgb(18, 46, 44) !important;
        }

        /* Map/land areas color */
        #leaflet-map .leaflet-tile-container img {
          filter: brightness(40%) sepia(30%) hue-rotate(120deg) contrast(130%) saturate(120%);
        }
        #leaflet-map .leaflet-tile-container {
          opacity: 0.9;
        }

        /* Country border lines */
        .leaflet-container path {
          stroke: rgb(76, 159, 123);
          stroke-width: 1px;
          fill: rgb(30, 65, 51);
        }

        /* Ensure path connections have no fill */
        .leaflet-overlay-pane path.leaflet-interactive {
          fill-opacity: 0 !important;
        }

        /* Data points with white glow */
        .data-point {
          fill: rgba(255, 255, 255, 0.9);
          fill-opacity: 0.9;
          stroke: #ffffff;
          stroke-width: 1.5;
          filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.8));
        }

        /* Source points */
        .source-point {
          fill: rgba(255, 255, 255, 0.9);
          fill-opacity: 0.9;
          stroke: #ffffff;
          stroke-width: 1.5;
          filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.8));
        }

        /* Target points */
        .target-point {
          fill: rgba(255, 255, 255, 0.9);
          fill-opacity: 0.9;
          stroke: #ffffff;
          stroke-width: 1.5;
          filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.8));
        }

        /* Connection lines - simple 3D curved lines with white glow */
        .connection-line {
          stroke: #ffffff;
          stroke-opacity: 0.8;
          stroke-width: 1.5;
          fill: none !important;
          fill-opacity: 0 !important;
          pointer-events: stroke;
          filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.6));
        }

        /* Pure line path class for path lines */
        .pure-line-path {
          stroke: #ffffff;
          stroke-opacity: 0.8;
          stroke-width: 1.5;
          fill: none !important;
          fill-opacity: 0 !important;
          pointer-events: stroke;
          filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.6));
        }

        /* Override any SVG path fill properties */
        .connection-line path, .pure-line-path path, .leaflet-overlay-pane path {
          fill: none !important;
          fill-opacity: 0 !important;
        }

        /* Animation for the path lines */
        .animated-line {
          stroke-dasharray: 5, 5;
          animation: dash 2s linear infinite;
        }

        @keyframes dash {
          to {
            stroke-dashoffset: -10;
          }
        }

        /* Hide attribution control */
        .leaflet-control-attribution {
          display: none;
        }
      `;
      document.head.appendChild(styleElement);

      // Render the paths
      this.renderPaths();
    },
    renderPaths() {
      if (!this.map || !window.L) return;

      // Clear existing paths and markers
      this.clearMapLayers();

      // Add path lines and markers
      this.getPathData.forEach(path => {
        // Convert coordinates from [lng, lat] to [lat, lng] for Leaflet
        const sourceCoords = [path.coords[0][1], path.coords[0][0]];
        const targetCoords = [path.coords[1][1], path.coords[1][0]];

        // Calculate distance between points to determine curvature
        const dx = targetCoords[1] - sourceCoords[1];
        const dy = targetCoords[0] - sourceCoords[0];
        const distance = Math.sqrt(dx * dx + dy * dy);

        // Create curved path line
        const curvedPath = this.createCurvedPath(sourceCoords, targetCoords, distance);

        // Create a pure line trajectory without any fill
        // Use a custom approach to create a line-only path

        // First, create a custom SVG renderer
        const svgRenderer = L.svg({ padding: 0.5 });

        // Create a simple polyline with minimal options
        const pathLine = L.polyline(curvedPath, {
          className: 'connection-line',
          weight: Math.max(1.5, path.value / 150),
          opacity: 0.8,
          color: '#ffffff',
          stroke: true,
          fill: false,
          renderer: svgRenderer,
          interactive: true,
          noClip: true
        }).addTo(this.map);

        // Force the path to have no fill after it's added to the map
        if (pathLine._path) {
          // Apply direct SVG attributes to ensure it's just a line
          pathLine._path.setAttribute('fill', 'none');
          pathLine._path.setAttribute('fill-opacity', '0');
          pathLine._path.setAttribute('stroke', '#ffffff');
          pathLine._path.setAttribute('stroke-opacity', '0.8');
          pathLine._path.setAttribute('vector-effect', 'non-scaling-stroke');
        }

        // Add a custom class to identify this as a pure line
        if (pathLine._container) {
          pathLine._container.classList.add('pure-line-path');
        }

        // Add tooltip to path line
        pathLine.bindTooltip(path.name);
        this.pathLines.push(pathLine);

        // Create source marker with white glow
        const sourceMarker = L.circleMarker(sourceCoords, {
          radius: Math.max(4, path.value / 60),
          className: 'source-point'
        }).addTo(this.map);

        // Add tooltip to source marker
        sourceMarker.bindTooltip(path.name.split(' to ')[0]);
        this.sourceMarkers.push(sourceMarker);

        // Create target marker with white glow
        const targetMarker = L.circleMarker(targetCoords, {
          radius: Math.max(4, path.value / 60),
          className: 'target-point'
        }).addTo(this.map);

        // Add tooltip to target marker
        targetMarker.bindTooltip(path.name.split(' to ')[1]);
        this.targetMarkers.push(targetMarker);

        // Create animated effect along the curved path - simple white dots
        this.createPathAnimation(curvedPath, path.value);
      });
    },

    // Helper method to create a curved path between two points
    createCurvedPath(source, target, distance) {
      // Calculate the number of points to use for the curve
      // More points = smoother curve
      const numPoints = Math.max(60, Math.ceil(distance * 4)); // Increased for smoother curves
      const curvedPath = [];

      // Calculate the midpoint
      const midX = (source[1] + target[1]) / 2;
      const midY = (source[0] + target[0]) / 2;

      // Calculate perpendicular offset for the control point
      // The offset determines how much the curve bends
      const dx = target[1] - source[1];
      const dy = target[0] - source[0];

      // Determine the direction and height of the curve based on the distance
      // This creates a more natural 3D-like curve for paths crossing the globe
      let curveFactor;

      // For paths crossing the international date line, curve in the opposite direction
      if (Math.abs(dx) > 180) {
        curveFactor = -0.25;
      } else {
        // Adjust curve height based on distance - longer distances get higher curves
        curveFactor = Math.min(0.3, 0.1 + (distance / 200));
      }

      // Calculate control point offset perpendicular to the line
      const offsetX = -dy * curveFactor;
      const offsetY = dx * curveFactor;

      // Control point for the quadratic Bezier curve
      const ctrlX = midX + offsetX;
      const ctrlY = midY + offsetY;

      // Generate points along the quadratic Bezier curve
      for (let i = 0; i <= numPoints; i++) {
        const t = i / numPoints;

        // Quadratic Bezier curve formula
        const x = (1 - t) * (1 - t) * source[1] + 2 * (1 - t) * t * ctrlX + t * t * target[1];
        const y = (1 - t) * (1 - t) * source[0] + 2 * (1 - t) * t * ctrlY + t * t * target[0];

        curvedPath.push([y, x]);
      }

      return curvedPath;
    },
    createPathAnimation(curvedPath, value) {
      if (!this.map || !window.L || !curvedPath || curvedPath.length === 0) return;

      // Create multiple moving dots along the curved path for a more subtle effect
      const createAnimatedDot = (startIndex, speed) => {
        // Create a smaller moving dot along the curved path
        const animatedMarker = L.circleMarker(curvedPath[startIndex], {
          radius: Math.max(2, value / 200), // Smaller radius for a more subtle effect
          className: 'data-point',
          fillOpacity: 0.7, // More transparent
          stroke: true,
          weight: 1
        }).addTo(this.map);

        // Animation function
        let pathIndex = startIndex;
        const totalPoints = curvedPath.length;

        const interval = setInterval(() => {
          pathIndex = (pathIndex + 1) % totalPoints;

          // Update marker position to the next point on the curved path
          animatedMarker.setLatLng(curvedPath[pathIndex]);
        }, speed); // Variable speed for different dots

        // Store the interval ID for cleanup
        animatedMarker.intervalId = interval;
        this.sourceMarkers.push(animatedMarker);
      };

      // Create 2-3 dots with different starting positions and speeds
      // for a more natural flow effect
      createAnimatedDot(0, 60); // First dot

      // Only add more dots for higher value paths
      if (value > 50) {
        createAnimatedDot(Math.floor(curvedPath.length / 3), 70); // Second dot
      }

      if (value > 100) {
        createAnimatedDot(Math.floor(curvedPath.length * 2 / 3), 50); // Third dot
      }
    },
    clearMapLayers() {
      // Clear path lines
      this.pathLines.forEach(line => {
        if (this.map) {
          this.map.removeLayer(line);
        }
      });
      this.pathLines = [];

      // Clear source markers
      this.sourceMarkers.forEach(marker => {
        if (marker.intervalId) {
          clearInterval(marker.intervalId);
        }
        if (this.map) {
          this.map.removeLayer(marker);
        }
      });
      this.sourceMarkers = [];

      // Clear target markers
      this.targetMarkers.forEach(marker => {
        if (this.map) {
          this.map.removeLayer(marker);
        }
      });
      this.targetMarkers = [];
    },
    // Removed zoom and pan methods as they are no longer needed
  },
  watch: {
    getPathData() {
      this.renderPaths();
    }
  }
}
</script>

<style scoped>
.map-container {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 10px;
  width: 50%;
}

.map-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 16px;
}

.active-title {
  color: #00FFCC;
  font-weight: bold;
}

.inactive-title {
  color: #aaa;
  cursor: pointer;
}

.divider {
  margin: 0 10px;
  color: #aaa;
}

.map-chart {
  flex: 1;
  width: 100%;
  border: 1px solid rgba(76, 159, 123, 0.3);
  border-radius: 4px;
  overflow: hidden;
  background-color: rgb(18, 46, 44) !important;
  box-shadow: inset 0 0 20px rgba(76, 159, 123, 0.1);
  position: relative;
}

/* Add grid overlay for tech aesthetic */
.map-chart::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(76, 159, 123, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(76, 159, 123, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
  z-index: 10;
}

/* Add glow effect */
.map-chart::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  box-shadow: inset 0 0 50px rgba(76, 159, 123, 0.1);
  pointer-events: none;
  z-index: 11;
}

.map-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1000;
}

.control-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.5);
  border: 1px solid #00FFCC;
  border-radius: 4px;
  color: #00FFCC;
  cursor: pointer;
  transition: all 0.3s;
}

.control-btn:hover {
  background-color: rgba(0, 255, 204, 0.2);
}

.control-icon {
  font-style: normal;
  font-size: 16px;
}
</style>
