# 3D Path Map Visualization Platform

A large screen visualization platform with world map data visualization, developed using Vue 2 framework.

## Features

- Combined display of a flat world map with 3D path trajectories
- Responsive design optimized for large screen displays
- Interactive data visualization with charts and maps
- Dark tech aesthetic with high-contrast colors
- Modular component structure for easy maintenance

## Project Structure

- `src/components/`: Vue components
- `src/store/`: Vuex store modules
- `src/assets/`: Static assets
- `src/utils/`: Utility functions
- `src/data/`: Mock data

## Main Components

1. **Left Panel**: Data visualization charts
2. **Map Container**: World map with 3D path trajectories
3. **Right Panel**: Function controls and data filtering
4. **Bottom Navigation**: Module navigation

## Technical Implementation

- Vue 2 framework
- Vuex for state management
- ECharts and ECharts-GL for visualization
- Responsive design for different screen sizes

## Project Setup

```bash
# Install dependencies
npm install

# Serve with hot reload at localhost:8080
npm run serve

# Build for production
npm run build
```

## Browser Compatibility

- Chrome
- Firefox
- Safari
