<template>
  <div class="standalone-global-distribution">
    <header class="page-header">
      <h1>全球分布 (Standalone Component)</h1>
      <div class="header-controls">
        <button @click="toggleView" class="toggle-btn">
          {{ showComparison ? 'Hide' : 'Show' }} Comparison
        </button>
      </div>
    </header>
    
    <div class="page-content">
      <!-- Standalone World Map -->
      <div class="map-container">
        <standalone-world-map
          :path-data="pathData"
          :height="'calc(100vh - 60px)'"
          :animation-enabled="true"
          :show-title="false"
          @map-ready="onMapReady"
        />
      </div>
      
      <!-- Comparison Panel (optional) -->
      <div v-if="showComparison" class="comparison-panel">
        <h2>Component Comparison</h2>
        <div class="comparison-content">
          <div class="comparison-item">
            <h3>Original Implementation</h3>
            <ul>
              <li>❌ Vuex dependency</li>
              <li>❌ Complex state management</li>
              <li>❌ Tightly coupled to project structure</li>
              <li>✅ ECharts 3D visualization</li>
            </ul>
          </div>
          
          <div class="comparison-item">
            <h3>Standalone Component</h3>
            <ul>
              <li>✅ No external dependencies</li>
              <li>✅ Self-contained and reusable</li>
              <li>✅ Easy integration</li>
              <li>✅ Leaflet-based with custom styling</li>
              <li>✅ Configurable props</li>
              <li>✅ Dark tech aesthetic maintained</li>
            </ul>
          </div>
        </div>
        
        <div class="integration-example">
          <h3>Integration Code</h3>
          <pre><code>&lt;template&gt;
  &lt;standalone-world-map
    :path-data="pathData"
    :height="'100vh'"
    :animation-enabled="true"
    @map-ready="onMapReady"
  /&gt;
&lt;/template&gt;

&lt;script&gt;
import StandaloneWorldMap from '@/components/StandaloneWorldMap.vue'

export default {
  components: { StandaloneWorldMap },
  data() {
    return {
      pathData: [
        {
          coords: [[39.9042, 116.4074], [40.7128, -74.0060]],
          value: 200,
          name: 'Beijing to New York'
        }
      ]
    }
  }
}
&lt;/script&gt;</code></pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import StandaloneWorldMap from '@/components/StandaloneWorldMap.vue'

export default {
  name: 'StandaloneGlobalDistribution',
  components: {
    StandaloneWorldMap
  },
  data() {
    return {
      showComparison: false,
      pathData: [
        {
          coords: [[39.9042, 116.4074], [40.7128, -74.0060]], // Beijing to New York
          value: 200,
          name: 'Beijing to New York'
        },
        {
          coords: [[51.5074, -0.1278], [35.6762, 139.6503]], // London to Tokyo
          value: 180,
          name: 'London to Tokyo'
        },
        {
          coords: [[-33.8688, 151.2093], [1.3521, 103.8198]], // Sydney to Singapore
          value: 150,
          name: 'Sydney to Singapore'
        },
        {
          coords: [[55.7558, 37.6173], [28.7041, 77.1025]], // Moscow to Delhi
          value: 120,
          name: 'Moscow to Delhi'
        },
        {
          coords: [[48.8566, 2.3522], [34.0522, -118.2437]], // Paris to Los Angeles
          value: 160,
          name: 'Paris to Los Angeles'
        },
        {
          coords: [[13.4050, 52.5200], [-22.9068, -43.1729]], // Berlin to Rio
          value: 140,
          name: 'Berlin to Rio de Janeiro'
        }
      ]
    }
  },
  methods: {
    toggleView() {
      this.showComparison = !this.showComparison
    },
    
    onMapReady(map) {
      console.log('Standalone world map is ready:', map)
      console.log('Features:')
      console.log('- Zoom control disabled:', !map.zoomControl)
      console.log('- Dragging disabled:', !map.dragging.enabled())
      console.log('- Touch zoom disabled:', !map.touchZoom.enabled())
      console.log('- Scroll wheel zoom disabled:', !map.scrollWheelZoom.enabled())
    }
  }
}
</script>

<style scoped>
.standalone-global-distribution {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #0F2E2C;
  color: #fff;
}

.page-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  background-color: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(0, 255, 204, 0.3);
}

.page-header h1 {
  font-size: 24px;
  font-weight: 500;
  color: #00FFCC;
}

.toggle-btn {
  padding: 8px 16px;
  background-color: rgba(0, 255, 204, 0.1);
  border: 1px solid #00FFCC;
  color: #00FFCC;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  background-color: rgba(0, 255, 204, 0.2);
}

.page-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.map-container {
  flex: 1;
  position: relative;
}

.comparison-panel {
  width: 400px;
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.3);
  border-left: 1px solid rgba(0, 255, 204, 0.3);
  overflow-y: auto;
}

.comparison-panel h2 {
  color: #00FFCC;
  margin-bottom: 20px;
  font-size: 20px;
}

.comparison-content {
  margin-bottom: 30px;
}

.comparison-item {
  margin-bottom: 20px;
  padding: 15px;
  background-color: rgba(0, 255, 204, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(0, 255, 204, 0.1);
}

.comparison-item h3 {
  color: #00FFCC;
  margin-bottom: 10px;
  font-size: 16px;
}

.comparison-item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.comparison-item li {
  padding: 4px 0;
  font-size: 14px;
  color: #ccc;
}

.integration-example {
  background-color: rgba(0, 0, 0, 0.4);
  border-radius: 6px;
  overflow: hidden;
}

.integration-example h3 {
  color: #00FFCC;
  padding: 15px 20px;
  margin: 0;
  background-color: rgba(0, 255, 204, 0.1);
  border-bottom: 1px solid rgba(0, 255, 204, 0.2);
  font-size: 16px;
}

.integration-example pre {
  margin: 0;
  padding: 20px;
  overflow-x: auto;
}

.integration-example code {
  color: #e6e6e6;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}
</style>
