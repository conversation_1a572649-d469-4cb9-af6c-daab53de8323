<template>
  <div class="dashboard">
    <header class="dashboard-header">中老特色农作物种质资源联合实验室种质资源数字信息管理平台</header>
    <div class="dashboard-content">
      <left-panel />
      <map-container />
      <right-panel />
    </div>
    <bottom-nav />
  </div>
</template>

<script>
import LeftPanel from './components/LeftPanel.vue'
import MapContainer from './components/MapContainer.vue'
import RightPanel from './components/RightPanel.vue'
import BottomNav from './components/BottomNav.vue'

export default {
  name: 'App',
  components: {
    LeftPanel,
    MapContainer,
    RightPanel,
    BottomNav
  }
}
</script>

<style>
body {
  margin: 0;
  padding: 0;
  font-family: 'Microsoft YaHei', sans-serif;
  background-color: #0F2E2C;
  color: #fff;
  overflow: hidden;
}

.dashboard {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-image: radial-gradient(circle at 50% 50%, rgba(0, 255, 204, 0.05) 0%, rgba(0, 0, 0, 0) 70%),
    linear-gradient(to bottom, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0) 20%);
}

.dashboard-header {
  height: 60px;
  text-align: center;
  line-height: 60px;
  font-size: 24px;
  font-weight: bold;
  background-color: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(0, 255, 204, 0.3);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 10;
}

.dashboard-header::before,
.dashboard-header::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 100px;
  height: 1px;
  background: linear-gradient(to right, transparent, #00FFCC, transparent);
}

.dashboard-header::before {
  left: 20%;
}

.dashboard-header::after {
  right: 20%;
}

.dashboard-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* Grid overlay for tech aesthetic */
.dashboard::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(0, 255, 204, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 204, 0.05) 1px, transparent 1px);
  background-size: 50px 50px;
  pointer-events: none;
  z-index: 1;
}

/* Global scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 204, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 204, 0.7);
}

/* Animations for tech aesthetic */
@keyframes pulse {
  0% {
    opacity: 0.7;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.7;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.dashboard {
  animation: fadeIn 1s ease-out;
}
</style>