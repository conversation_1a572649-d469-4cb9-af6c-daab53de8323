<template>
  <div class="visualization-platform">
    <header class="platform-header">
      <h1>{{ title }}</h1>
    </header>
    
    <main class="platform-content">
      <!-- Left Data Panel -->
      <section class="data-panel left-panel">
        <div class="panel-section">
          <h2>种类分类数据统计</h2>
          <div class="chart-container pie-chart">
            <div ref="pieChart" class="chart"></div>
          </div>
        </div>
        
        <div class="panel-section">
          <div class="category-label">
            <div class="category-icon"></div>
            <span>农作物</span>
          </div>
          <div class="bar-charts">
            <div v-for="(item, index) in forestryData" :key="index" class="bar-chart-item">
              <span class="bar-label">{{ item.name }}</span>
              <div class="bar-container">
                <div class="bar" :style="{ width: `${item.percentage}%` }"></div>
              </div>
              <span class="bar-value">{{ item.value }}</span>
            </div>
          </div>
        </div>
        
        <div class="panel-section">
          <div class="category-label">
            <div class="category-icon"></div>
            <span>其他作物</span>
          </div>
          <div class="semi-circle-chart">
            <div ref="semiCircleChart" class="chart"></div>
          </div>
        </div>
      </section>
      
      <!-- Central Map Area -->
      <section class="map-panel">
        <div class="map-controls">
          <div class="control-button active">库存种子</div>
          <div class="control-button">活体植株</div>
        </div>
        <div ref="worldMap" class="world-map"></div>
        <div class="bottom-navigation">
          <div v-for="(nav, index) in navigationItems" :key="index" 
               class="nav-item" :class="{ active: index === 3 }">
            <div class="nav-icon">
              <component :is="nav.icon" />
            </div>
          </div>
        </div>
      </section>
      
      <!-- Right Function Panel -->
      <section class="data-panel right-panel">
        <h2>库存种子</h2>
        <div class="circular-navigation">
          <div v-for="(item, index) in seedCategories" :key="index" class="circular-item">
            <div class="circular-image" :style="{ backgroundImage: `url(${item.image})` }"></div>
            <span class="circular-label">{{ item.name }}</span>
          </div>
        </div>
        <div class="pagination">
          <span>共6532条</span>
          <div class="pagination-controls">
            <button class="pagination-arrow">&lt;</button>
            <button class="pagination-number active">1</button>
            <button class="pagination-number">2</button>
            <button class="pagination-number">3</button>
            <button class="pagination-number">4</button>
            <button class="pagination-number">5</button>
            <span>...</span>
            <button class="pagination-number">6</button>
            <button class="pagination-arrow">&gt;</button>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts-gl'
import { Home, Database, Compass, Map, Box, Building, Globe } from 'lucide-vue-next'

export default {
  name: 'WorldMapVisualization',
  components: {
    Home,
    Database,
    Compass,
    Map,
    Box,
    Building,
    Globe
  },
  data() {
    return {
      title: '中老特色农林作物种质资源联合实验室种质资源数字信息管理平台',
      pieChartInstance: null,
      worldMapInstance: null,
      semiCircleChartInstance: null,
      cropData: [
        { name: '粮食作物', value: 560, color: '#5AD8A6' },
        { name: '油料作物', value: 423, color: '#5B8FF9' },
        { name: '纤维作物', value: 410, color: '#5D7092' },
        { name: '蔬菜', value: 320, color: '#F6BD16' },
        { name: '果树', value: 248, color: '#E8684A' },
        { name: '花卉', value: 103, color: '#FF9D4D' }
      ],
      forestryData: [
        { name: '落木', value: 180, percentage: 90 },
        { name: '乔木', value: 120, percentage: 60 },
        { name: '草本', value: 160, percentage: 80 },
        { name: '竹类', value: 80, percentage: 40 },
        { name: '灌木', value: 60, percentage: 30 }
      ],
      otherCropsData: [
        { name: '棕榈树', value: 320, icon: 'tree' },
        { name: '浆果', value: 120, icon: 'fruit' },
        { name: '油料', value: 90, icon: 'oil' },
        { name: '咖啡', value: 200, icon: 'coffee' },
        { name: '热带收获', value: 56, icon: 'tropical' },
        { name: '其他作物', value: 30, icon: 'other' }
      ],
      seedCategories: [
        { name: '水稻', image: '/placeholder.svg?height=80&width=80' },
        { name: '小麦', image: '/placeholder.svg?height=80&width=80' },
        { name: '高粱', image: '/placeholder.svg?height=80&width=80' },
        { name: '玉米', image: '/placeholder.svg?height=80&width=80' },
        { name: '大豆', image: '/placeholder.svg?height=80&width=80' },
        { name: '豇豆', image: '/placeholder.svg?height=80&width=80' },
        { name: '鹰嘴豆', image: '/placeholder.svg?height=80&width=80' },
        { name: '黑芝麻', image: '/placeholder.svg?height=80&width=80' },
        { name: '白芝麻', image: '/placeholder.svg?height=80&width=80' }
      ],
      navigationItems: [
        { name: '首页', icon: 'Home' },
        { name: '数据库', icon: 'Database' },
        { name: '导航', icon: 'Compass' },
        { name: '地图', icon: 'Map', active: true },
        { name: '资源', icon: 'Box' },
        { name: '机构', icon: 'Building' },
        { name: '全球', icon: 'Globe' }
      ]
    }
  },
  mounted() {
    this.initPieChart()
    this.initWorldMap()
    this.initSemiCircleChart()
    
    // Add responsive handling
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    // Clean up event listeners
    window.removeEventListener('resize', this.handleResize)
    
    // Dispose chart instances
    if (this.pieChartInstance) {
      this.pieChartInstance.dispose()
    }
    if (this.worldMapInstance) {
      this.worldMapInstance.dispose()
    }
    if (this.semiCircleChartInstance) {
      this.semiCircleChartInstance.dispose()
    }
  },
  methods: {
    handleResize() {
      if (this.pieChartInstance) {
        this.pieChartInstance.resize()
      }
      if (this.worldMapInstance) {
        this.worldMapInstance.resize()
      }
      if (this.semiCircleChartInstance) {
        this.semiCircleChartInstance.resize()
      }
    },
    initPieChart() {
      this.pieChartInstance = echarts.init(this.$refs.pieChart)
      
      const option = {
        backgroundColor: 'transparent',
        color: this.cropData.map(item => item.color),
        title: {
          text: '2,064',
          left: 'center',
          top: 'center',
          textStyle: {
            color: '#fff',
            fontSize: 24,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 0,
          top: 'center',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: '#fff',
            fontSize: 12
          },
          formatter: name => {
            const item = this.cropData.find(item => item.name === name)
            return `{a|${name}} {b|${item.value}}`
          },
          textStyle: {
            rich: {
              a: {
                color: '#fff',
                width: 60,
                fontSize: 12
              },
              b: {
                color: '#FFB800',
                width: 40,
                align: 'right',
                fontSize: 12
              }
            }
          }
        },
        series: [
          {
            name: '作物分类',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['40%', '50%'],
            avoidLabelOverlap: false,
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false
              }
            },
            labelLine: {
              show: false
            },
            data: this.cropData.map(item => ({
              name: item.name,
              value: item.value
            }))
          }
        ]
      }
      
      this.pieChartInstance.setOption(option)
    },
    initWorldMap() {
      this.worldMapInstance = echarts.init(this.$refs.worldMap)
      
      // Fetch world map GeoJSON data
      fetch('https://raw.githubusercontent.com/apache/echarts/master/test/data/asset/geo/world.json')
        .then(response => response.json())
        .then(worldJson => {
          echarts.registerMap('world', worldJson)
          
          const option = {
            backgroundColor: 'transparent',
            geo: {
              map: 'world',
              roam: true,
              center: [0, 30],
              zoom: 1.2,
              silent: true,
              itemStyle: {
                areaColor: 'transparent',
                borderColor: '#00FFCC',
                borderWidth: 1
              },
              emphasis: {
                itemStyle: {
                  areaColor: 'rgba(0, 255, 204, 0.2)',
                  borderColor: '#00FFCC',
                  borderWidth: 1
                }
              }
            },
            series: [
              {
                type: 'lines3D',
                coordinateSystem: 'geo3D',
                effect: {
                  show: true,
                  period: 4,
                  trailLength: 0.7,
                  color: '#0088FF',
                  symbolSize: 3
                },
                lineStyle: {
                  width: 2,
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [{
                      offset: 0, color: '#0088FF'
                    }, {
                      offset: 1, color: '#00FFAA'
                    }]
                  },
                  opacity: 0.8,
                  curveness: 0.3
                },
                data: this.generatePathData()
              },
              {
                type: 'effectScatter',
                coordinateSystem: 'geo',
                symbolSize: 8,
                itemStyle: {
                  color: '#00FFCC'
                },
                rippleEffect: {
                  brushType: 'stroke',
                  scale: 4,
                  period: 4
                },
                data: this.generateScatterData()
              }
            ]
          }
          
          this.worldMapInstance.setOption(option)
        })
    },
    initSemiCircleChart() {
      this.semiCircleChartInstance = echarts.init(this.$refs.semiCircleChart)
      
      const option = {
        backgroundColor: 'transparent',
        title: {
          text: '816',
          left: 'center',
          top: 'center',
          textStyle: {
            color: '#fff',
            fontSize: 24,
            fontWeight: 'bold'
          }
        },
        series: [
          {
            type: 'gauge',
            startAngle: 180,
            endAngle: 0,
            radius: '100%',
            center: ['50%', '80%'],
            axisLine: {
              lineStyle: {
                width: 20,
                color: [
                  [0.4, '#5AD8A6'],
                  [0.7, '#5B8FF9'],
                  [1, '#F6BD16']
                ]
              }
            },
            pointer: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            splitLine: {
              show: false
            },
            detail: {
              show: false
            },
            data: [
              {
                value: 70
              }
            ]
          }
        ]
      }
      
      this.semiCircleChartInstance.setOption(option)
    },
    generatePathData() {
      // Generate sample path data for demonstration
      const paths = []
      const majorCities = [
        [116.4, 39.9], // Beijing
        [121.4, 31.2], // Shanghai
        [103.8, 1.4], // Singapore
        [77.2, 28.6], // New Delhi
        [37.6, 55.7], // Moscow
        [30.3, 59.9], // St Petersburg
        [28.0, -26.2], // Johannesburg
        [2.3, 48.9], // Paris
        [-0.1, 51.5], // London
        [-74.0, 40.7], // New York
        [-118.2, 34.1], // Los Angeles
        [-43.2, -22.9], // Rio de Janeiro
        [139.7, 35.7], // Tokyo
        [151.2, -33.9], // Sydney
      ]
      
      // Create paths between random cities
      for (let i = 0; i < 20; i++) {
        const source = majorCities[Math.floor(Math.random() * majorCities.length)]
        let target = majorCities[Math.floor(Math.random() * majorCities.length)]
        
        // Ensure source and target are different
        while (source === target) {
          target = majorCities[Math.floor(Math.random() * majorCities.length)]
        }
        
        paths.push({
          coords: [source, target],
          value: Math.random() * 100
        })
      }
      
      return paths
    },
    generateScatterData() {
      // Generate sample scatter data for demonstration
      return [
        { name: 'Beijing', value: [116.4, 39.9, 100] },
        { name: 'Shanghai', value: [121.4, 31.2, 80] },
        { name: 'Singapore', value: [103.8, 1.4, 60] },
        { name: 'New Delhi', value: [77.2, 28.6, 70] },
        { name: 'Moscow', value: [37.6, 55.7, 50] },
        { name: 'London', value: [-0.1, 51.5, 90] },
        { name: 'New York', value: [-74.0, 40.7, 85] },
        { name: 'Tokyo', value: [139.7, 35.7, 95] },
        { name: 'Sydney', value: [151.2, -33.9, 65] }
      ]
    }
  }
}
</script>

<style>
/* Base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', 'Open Sans', sans-serif;
  background-color: #0F2E2C;
  color: #fff;
  overflow: hidden;
}

.visualization-platform {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Header */
.platform-header {
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 20px;
  background-color: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(0, 255, 204, 0.3);
}

.platform-header h1 {
  font-size: 24px;
  font-weight: 500;
  color: #fff;
  text-align: center;
}

/* Main content */
.platform-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* Panels */
.data-panel {
  width: 25%;
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.panel-section {
  margin-bottom: 30px;
}

.panel-section h2 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #00FFCC;
}

.chart-container {
  width: 100%;
  height: 250px;
}

/* Map panel */
.map-panel {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
}

.map-controls {
  display: flex;
  justify-content: center;
  padding: 10px 0;
}

.control-button {
  padding: 5px 15px;
  margin: 0 10px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  border: 1px solid rgba(0, 255, 204, 0.5);
  transition: all 0.3s ease;
}

.control-button.active {
  background-color: rgba(0, 255, 204, 0.2);
  border-color: #00FFCC;
}

.world-map {
  flex: 1;
  width: 100%;
}

/* Bar charts */
.bar-charts {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.bar-chart-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.bar-label {
  width: 40px;
  font-size: 14px;
  text-align: right;
}

.bar-container {
  flex: 1;
  height: 8px;
  background-color: rgba(255, 255, 255, 0.1);
}

.bar {
  height: 100%;
  background-color: #00FFCC;
}

.bar-value {
  width: 30px;
  font-size: 14px;
  color: #00FFCC;
}

/* Category labels */
.category-label {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.category-icon {
  width: 20px;
  height: 20px;
  background-color: #00FFCC;
  margin-right: 10px;
}

/* Circular navigation */
.circular-navigation {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}

.circular-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80px;
}

.circular-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  border: 2px solid #00FFCC;
  margin-bottom: 10px;
}

.circular-label {
  font-size: 14px;
  text-align: center;
}

/* Bottom navigation */
.bottom-navigation {
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 30px;
  background-color: rgba(0, 0, 0, 0.3);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.nav-icon {
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  background-color: rgba(0, 255, 204, 0.1);
  color: #00FFCC;
  transition: all 0.3s ease;
}

.nav-item.active .nav-icon {
  background-color: rgba(0, 255, 204, 0.3);
  box-shadow: 0 0 15px rgba(0, 255, 204, 0.5);
}

/* Pagination */
.pagination {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.pagination-number, .pagination-arrow {
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid rgba(0, 255, 204, 0.3);
  background-color: transparent;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-number.active {
  background-color: rgba(0, 255, 204, 0.3);
  border-color: #00FFCC;
}

/* Semi-circle chart */
.semi-circle-chart {
  width: 100%;
  height: 150px;
}

/* Responsive adjustments */
@media (max-width: 1600px) {
  .circular-item {
    width: 70px;
  }
  
  .circular-image {
    width: 70px;
    height: 70px;
  }
  
  .circular-label {
    font-size: 12px;
  }
}

@media (max-width: 1200px) {
  .platform-content {
    flex-direction: column;
  }
  
  .data-panel {
    width: 100%;
    height: 300px;
  }
  
  .map-panel {
    height: calc(100vh - 660px);
  }
}
</style>