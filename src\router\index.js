import Vue from 'vue'
import VueRouter from 'vue-router'
import GlobalDistribution from '@/views/GlobalDistribution.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import(/* webpackChunkName: "home" */ '@/components/HomeView.vue')
  },
  {
    path: '/global-distribution',
    name: 'GlobalDistribution',
    component: GlobalDistribution
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

export default router
