<template>
  <div class="standalone-world-map" :style="containerStyle">
    <div class="map-title" v-if="showTitle">
      <span class="title-text">{{ title }}</span>
    </div>
    <div :id="mapId" class="map-container" :style="mapContainerStyle"></div>
  </div>
</template>

<script>
export default {
  name: 'StandaloneWorldMap',
  props: {
    // Map configuration
    width: {
      type: [String, Number],
      default: '100%'
    },
    height: {
      type: [String, Number],
      default: '500px'
    },
    title: {
      type: String,
      default: 'Global Distribution'
    },
    showTitle: {
      type: Boolean,
      default: true
    },

    // Map behavior
    center: {
      type: Array,
      default: () => [20, 0]
    },
    zoom: {
      type: Number,
      default: 2
    },

    // Path data
    pathData: {
      type: Array,
      default: () => []
    },

    // Styling options
    colors: {
      type: Object,
      default: () => ({
        land: 'rgb(30, 65, 51)',
        borders: 'rgb(76, 159, 123)',
        ocean: 'rgb(18, 46, 44)',
        pathLine: '#ffffff',
        sourcePoint: '#ffffff',
        targetPoint: '#ffffff'
      })
    },

    // Animation settings
    animationEnabled: {
      type: Boolean,
      default: true
    },
    animationSpeed: {
      type: Number,
      default: 2000
    },

    // Grid overlay
    showGrid: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      map: null,
      pathLines: [],
      sourceMarkers: [],
      targetMarkers: [],
      leafletLoaded: false,
      svgObserver: null,
      mapId: `world-map-${Math.random().toString(36).substr(2, 9)}`
    }
  },
  computed: {
    containerStyle() {
      return {
        width: typeof this.width === 'number' ? `${this.width}px` : this.width,
        height: typeof this.height === 'number' ? `${this.height}px` : this.height
      }
    },
    mapContainerStyle() {
      return {
        backgroundColor: this.colors.ocean
      }
    }
  },
  mounted() {
    this.loadLeaflet()
  },
  beforeDestroy() {
    this.cleanup()
  },
  watch: {
    pathData: {
      handler() {
        if (this.map && this.leafletLoaded) {
          this.renderPaths()
        }
      },
      deep: true
    }
  },
  methods: {
    loadLeaflet() {
      // Check if Leaflet is already loaded
      if (window.L) {
        this.leafletLoaded = true
        this.initMap()
        return
      }

      // Dynamically load Leaflet CSS
      if (!document.querySelector('link[href*="leaflet.css"]')) {
        const leafletCss = document.createElement('link')
        leafletCss.rel = 'stylesheet'
        leafletCss.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css'
        document.head.appendChild(leafletCss)
      }

      // Dynamically load Leaflet JS
      if (!document.querySelector('script[src*="leaflet.js"]')) {
        const leafletScript = document.createElement('script')
        leafletScript.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js'
        leafletScript.onload = () => {
          this.leafletLoaded = true
          this.initMap()
        }
        document.head.appendChild(leafletScript)
      }
    },

    initMap() {
      if (!this.leafletLoaded || !window.L) return

      // Initialize the map with fixed zoom and disabled interactions
      this.map = window.L.map(this.mapId, {
        center: this.center,
        zoom: this.zoom,
        zoomControl: false,
        attributionControl: false,
        dragging: false,
        touchZoom: false,
        doubleClickZoom: false,
        scrollWheelZoom: false,
        boxZoom: false,
        keyboard: false,
        tap: false
      })

      // Add the tile layer
      window.L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; OpenStreetMap contributors'
      }).addTo(this.map)

      // Apply custom styling
      this.applyCustomStyling()

      // Setup SVG observer for path lines
      this.setupSVGObserver()

      // Render paths if data is available
      if (this.pathData.length > 0) {
        this.renderPaths()
      }

      // Emit ready event
      this.$emit('map-ready', this.map)
    },

    applyCustomStyling() {
      const styleId = `${this.mapId}-styles`

      // Remove existing styles if any
      const existingStyle = document.getElementById(styleId)
      if (existingStyle) {
        existingStyle.remove()
      }

      const styleElement = document.createElement('style')
      styleElement.id = styleId
      styleElement.textContent = `
        /* Map container background */
        #${this.mapId} {
          background-color: ${this.colors.ocean} !important;
        }
        #${this.mapId} .leaflet-container {
          background-color: ${this.colors.ocean} !important;
        }

        /* Map/land areas color */
        #${this.mapId} .leaflet-tile-container img {
          filter: brightness(40%) sepia(30%) hue-rotate(120deg) contrast(130%) saturate(120%);
        }
        #${this.mapId} .leaflet-tile-container {
          opacity: 0.9;
        }

        /* Country border lines */
        #${this.mapId} .leaflet-container path {
          stroke: ${this.colors.borders};
          stroke-width: 1px;
          fill: ${this.colors.land};
        }

        /* Ensure path connections have no fill */
        #${this.mapId} .leaflet-overlay-pane path.leaflet-interactive {
          fill-opacity: 0 !important;
        }

        /* Path line styling */
        #${this.mapId} .path-line {
          stroke: ${this.colors.pathLine} !important;
          stroke-width: 2px !important;
          fill: none !important;
          fill-opacity: 0 !important;
          filter: drop-shadow(0 0 3px ${this.colors.pathLine});
        }

        /* Source and target point styling */
        #${this.mapId} .source-point,
        #${this.mapId} .target-point {
          fill: ${this.colors.sourcePoint} !important;
          stroke: ${this.colors.sourcePoint} !important;
          stroke-width: 2px !important;
          filter: drop-shadow(0 0 5px ${this.colors.sourcePoint});
        }

        /* Grid overlay */
        ${this.showGrid ? `
        #${this.mapId}::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-image:
            linear-gradient(rgba(76, 159, 123, 0.05) 1px, transparent 1px),
            linear-gradient(90deg, rgba(76, 159, 123, 0.05) 1px, transparent 1px);
          background-size: 20px 20px;
          pointer-events: none;
          z-index: 10;
        }
        ` : ''}

        /* Glow effect */
        #${this.mapId}::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          box-shadow: inset 0 0 50px rgba(76, 159, 123, 0.1);
          pointer-events: none;
          z-index: 11;
        }
      `
      document.head.appendChild(styleElement)
    },

    setupSVGObserver() {
      // Create a MutationObserver to ensure path lines have no fill
      this.svgObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                // Find all path elements that might be path lines
                const paths = node.querySelectorAll ? node.querySelectorAll('path.leaflet-interactive') : []
                paths.forEach((path) => {
                  if (path.classList.contains('pure-line-path')) {
                    path.style.fill = 'none'
                    path.style.fillOpacity = '0'
                  }
                })
              }
            })
          }
        })
      })

      // Start observing the map container
      const mapContainer = document.getElementById(this.mapId)
      if (mapContainer) {
        this.svgObserver.observe(mapContainer, {
          childList: true,
          subtree: true
        })
      }
    },

    renderPaths() {
      if (!this.map || !window.L) return

      // Clear existing paths
      this.clearMapLayers()

      // Render each path
      this.pathData.forEach((path, index) => {
        this.renderSinglePath(path, index)
      })
    },

    renderSinglePath(path, index) {
      if (!path.coords || path.coords.length < 2) return

      const [sourceCoords, targetCoords] = path.coords

      // Create curved path using multiple points to simulate a curve
      const curvePoints = this.generateCurvePoints(sourceCoords, targetCoords)
      const pathLine = window.L.polyline(curvePoints, {
        color: this.colors.pathLine,
        weight: 2,
        opacity: 0.8,
        className: 'path-line pure-line-path'
      }).addTo(this.map)

      // Add tooltip to path line
      if (path.name) {
        pathLine.bindTooltip(path.name)
      }

      this.pathLines.push(pathLine)

      // Create source marker
      const sourceMarker = window.L.circleMarker(sourceCoords, {
        radius: Math.max(4, (path.value || 50) / 60),
        className: 'source-point',
        color: this.colors.sourcePoint,
        fillColor: this.colors.sourcePoint,
        fillOpacity: 0.8
      }).addTo(this.map)

      if (path.name) {
        const sourceName = path.name.split(' to ')[0] || 'Source'
        sourceMarker.bindTooltip(sourceName)
      }

      this.sourceMarkers.push(sourceMarker)

      // Create target marker
      const targetMarker = window.L.circleMarker(targetCoords, {
        radius: Math.max(4, (path.value || 50) / 60),
        className: 'target-point',
        color: this.colors.targetPoint,
        fillColor: this.colors.targetPoint,
        fillOpacity: 0.8
      }).addTo(this.map)

      if (path.name) {
        const targetName = path.name.split(' to ')[1] || 'Target'
        targetMarker.bindTooltip(targetName)
      }

      this.targetMarkers.push(targetMarker)

      // Add animation if enabled
      if (this.animationEnabled) {
        this.animateMarker(sourceMarker, index)
      }
    },

    generateCurvePoints(start, end, numPoints = 20) {
      const points = []

      // Calculate the midpoint
      const midLat = (start[0] + end[0]) / 2
      const midLng = (start[1] + end[1]) / 2

      // Calculate the distance for curve height
      const distance = Math.sqrt(
        Math.pow(end[0] - start[0], 2) + Math.pow(end[1] - start[1], 2)
      )

      // Add curvature based on distance
      const curvature = distance * 0.3

      // Determine if we should curve up or down based on hemisphere
      const curveDirection = midLat > 0 ? 1 : -1
      const controlPoint = [midLat + (curvature * curveDirection), midLng]

      // Generate points along the quadratic bezier curve
      for (let i = 0; i <= numPoints; i++) {
        const t = i / numPoints
        const lat = Math.pow(1 - t, 2) * start[0] + 2 * (1 - t) * t * controlPoint[0] + Math.pow(t, 2) * end[0]
        const lng = Math.pow(1 - t, 2) * start[1] + 2 * (1 - t) * t * controlPoint[1] + Math.pow(t, 2) * end[1]
        points.push([lat, lng])
      }

      return points
    },

    animateMarker(marker, index) {
      if (!marker || !this.animationEnabled) return

      let opacity = 0.3
      let increasing = true

      const animate = () => {
        if (increasing) {
          opacity += 0.02
          if (opacity >= 1) {
            increasing = false
          }
        } else {
          opacity -= 0.02
          if (opacity <= 0.3) {
            increasing = true
          }
        }

        if (marker._path) {
          marker._path.style.opacity = opacity
        }
      }

      // Start animation with a delay based on index
      setTimeout(() => {
        marker.intervalId = setInterval(animate, 50)
      }, index * 200)
    },

    clearMapLayers() {
      // Clear path lines
      this.pathLines.forEach(line => {
        if (this.map && line) {
          this.map.removeLayer(line)
        }
      })
      this.pathLines = []

      // Clear source markers
      this.sourceMarkers.forEach(marker => {
        if (marker.intervalId) {
          clearInterval(marker.intervalId)
        }
        if (this.map && marker) {
          this.map.removeLayer(marker)
        }
      })
      this.sourceMarkers = []

      // Clear target markers
      this.targetMarkers.forEach(marker => {
        if (this.map && marker) {
          this.map.removeLayer(marker)
        }
      })
      this.targetMarkers = []
    },

    cleanup() {
      // Clear all layers
      this.clearMapLayers()

      // Remove map
      if (this.map) {
        this.map.remove()
        this.map = null
      }

      // Disconnect SVG observer
      if (this.svgObserver) {
        this.svgObserver.disconnect()
        this.svgObserver = null
      }

      // Remove custom styles
      const styleElement = document.getElementById(`${this.mapId}-styles`)
      if (styleElement) {
        styleElement.remove()
      }
    },

    // Public methods for external control
    addPath(pathData) {
      const newPaths = Array.isArray(pathData) ? pathData : [pathData]
      this.$emit('update:pathData', [...this.pathData, ...newPaths])
    },

    removePath(index) {
      if (index >= 0 && index < this.pathData.length) {
        const newPaths = [...this.pathData]
        newPaths.splice(index, 1)
        this.$emit('update:pathData', newPaths)
      }
    },

    clearPaths() {
      this.$emit('update:pathData', [])
    },

    updateColors(newColors) {
      Object.assign(this.colors, newColors)
      if (this.map) {
        this.applyCustomStyling()
        this.renderPaths()
      }
    }
  }
}
</script>

<style scoped>
.standalone-world-map {
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #0F2E2C;
  border-radius: 8px;
  overflow: hidden;
}

.map-title {
  padding: 10px 15px;
  background-color: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(0, 255, 204, 0.3);
}

.title-text {
  color: #00FFCC;
  font-size: 16px;
  font-weight: 500;
}

.map-container {
  flex: 1;
  position: relative;
  min-height: 400px;
}
</style>
