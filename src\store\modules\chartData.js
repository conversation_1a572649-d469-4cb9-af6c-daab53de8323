// Store module for chart-related data
export default {
  namespaced: true,
  state: {
    // Ring chart data
    ringChartData: {
      totalTransactions: 2064,
      categories: [
        { name: '类别1', value: 500, color: '#FF6B6B' },
        { name: '类别2', value: 423, color: '#FFD166' },
        { name: '类别3', value: 410, color: '#06D6A0' },
        { name: '类别4', value: 325, color: '#118AB2' },
        { name: '类别5', value: 204, color: '#073B4C' },
        { name: '其他', value: 183, color: '#7F7F7F' }
      ]
    },
    // Bar chart data
    barChartData: {
      xAxis: ['一月', '二月', '三月', '四月', '五月', '六月'],
      series: [
        { name: '种类1', data: [180, 220, 160, 140, 190, 210] },
        { name: '种类2', data: [120, 140, 170, 150, 160, 130] },
        { name: '种类3', data: [90, 110, 100, 120, 130, 100] },
        { name: '种类4', data: [80, 70, 90, 110, 100, 90] },
        { name: '种类5', data: [60, 50, 70, 80, 70, 60] }
      ]
    },
    // Pie chart data
    pieChartData: {
      data: [
        { name: '区域1', value: 340 },
        { name: '区域2', value: 210 },
        { name: '区域3', value: 150 },
        { name: '区域4', value: 90 },
        { name: '区域5', value: 30 }
      ]
    },
    // Time range for filtering
    timeRange: {
      start: '2023-01-01',
      end: '2023-12-31'
    },
    // Selected categories for filtering
    selectedCategories: ['全部']
  },
  mutations: {
    SET_TIME_RANGE(state, range) {
      state.timeRange = range;
    },
    SET_SELECTED_CATEGORIES(state, categories) {
      state.selectedCategories = categories;
    }
  },
  actions: {
    // Update time range
    updateTimeRange({ commit }, range) {
      commit('SET_TIME_RANGE', range);
    },
    // Update selected categories
    updateSelectedCategories({ commit }, categories) {
      commit('SET_SELECTED_CATEGORIES', categories);
    }
  },
  getters: {
    getRingChartData: state => state.ringChartData,
    getBarChartData: state => state.barChartData,
    getPieChartData: state => state.pieChartData,
    getTimeRange: state => state.timeRange,
    getSelectedCategories: state => state.selectedCategories
  }
}
