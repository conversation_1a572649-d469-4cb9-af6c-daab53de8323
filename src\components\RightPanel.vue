<template>
  <div class="right-panel">
    <div class="panel-section">
      <div class="section-header">
        <i class="section-icon">◉</i>
        <span>资源种类</span>
      </div>
      <div class="resource-grid">
        <div v-for="(item, index) in resourceItems" :key="index" class="resource-item" :class="{ active: item.active }"
          @click="toggleResourceItem(index)">
          <div class="resource-image" :style="{ backgroundImage: `url(${item.image})` }"></div>
          <div class="resource-name">{{ item.name }}</div>
        </div>
      </div>
    </div>

    <div class="panel-section">
      <div class="section-header">
        <i class="section-icon">◉</i>
        <span>数据筛选</span>
      </div>
      <div class="filter-section">
        <div class="filter-label">时间范围</div>
        <div class="time-range-picker">
          <div class="time-input">
            <input type="text" v-model="timeRange.start" placeholder="开始日期">
          </div>
          <div class="time-separator">至</div>
          <div class="time-input">
            <input type="text" v-model="timeRange.end" placeholder="结束日期">
          </div>
        </div>
      </div>
      <div class="filter-section">
        <div class="filter-label">种类筛选</div>
        <div class="category-filter">
          <div v-for="(category, index) in categories" :key="index" class="category-item"
            :class="{ active: selectedCategories.includes(category) }" @click="toggleCategory(category)">
            {{ category }}
          </div>
        </div>
      </div>
      <div class="filter-buttons">
        <button class="filter-btn reset-btn" @click="resetFilters">重置</button>
        <button class="filter-btn apply-btn" @click="applyFilters">应用</button>
      </div>
    </div>

    <div class="panel-section">
      <div class="section-header">
        <i class="section-icon">◉</i>
        <span>图例说明</span>
      </div>
      <div class="legend-explanation">
        <div class="legend-item">
          <div class="legend-color" style="background: linear-gradient(to right, #0088FF, #00FFAA);"></div>
          <div class="legend-desc">路径流向 - 表示资源流动方向</div>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background-color: #00FFCC;"></div>
          <div class="legend-desc">区域边界 - 表示国家/地区边界</div>
        </div>
        <div class="legend-item">
          <div class="legend-color" style="background-color: rgba(0, 255, 204, 0.3);"></div>
          <div class="legend-desc">高亮区域 - 表示选中的区域</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'RightPanel',
  data() {
    return {
      resourceItems: [
        { name: '水稻', image: 'https://via.placeholder.com/80?text=水稻', active: true },
        { name: '小麦', image: 'https://via.placeholder.com/80?text=小麦', active: false },
        { name: '玉米', image: 'https://via.placeholder.com/80?text=玉米', active: false },
        { name: '大豆', image: 'https://via.placeholder.com/80?text=大豆', active: false },
        { name: '棉花', image: 'https://via.placeholder.com/80?text=棉花', active: false },
        { name: '油菜', image: 'https://via.placeholder.com/80?text=油菜', active: false },
        { name: '花生', image: 'https://via.placeholder.com/80?text=花生', active: false },
        { name: '蔬菜', image: 'https://via.placeholder.com/80?text=蔬菜', active: false },
        { name: '水果', image: 'https://via.placeholder.com/80?text=水果', active: false }
      ],
      categories: ['全部', '类别1', '类别2', '类别3', '类别4', '类别5'],
      localTimeRange: {
        start: '',
        end: ''
      },
      localSelectedCategories: []
    }
  },
  computed: {
    ...mapGetters('chartData', [
      'getTimeRange',
      'getSelectedCategories'
    ]),
    timeRange: {
      get() {
        return this.getTimeRange
      },
      set(value) {
        this.localTimeRange = value
      }
    },
    selectedCategories: {
      get() {
        return this.getSelectedCategories
      },
      set(value) {
        this.localSelectedCategories = value
      }
    }
  },
  methods: {
    ...mapActions('chartData', [
      'updateTimeRange',
      'updateSelectedCategories'
    ]),
    toggleResourceItem(index) {
      this.resourceItems = this.resourceItems.map((item, i) => ({
        ...item,
        active: i === index ? !item.active : item.active
      }))
    },
    toggleCategory(category) {
      let newSelectedCategories = [...this.selectedCategories]

      if (category === '全部') {
        // If "全部" is clicked, toggle between all and none
        newSelectedCategories = newSelectedCategories.includes('全部') ? [] : ['全部']
      } else {
        // Remove "全部" if it's selected
        if (newSelectedCategories.includes('全部')) {
          newSelectedCategories = newSelectedCategories.filter(c => c !== '全部')
        }

        // Toggle the selected category
        if (newSelectedCategories.includes(category)) {
          newSelectedCategories = newSelectedCategories.filter(c => c !== category)
        } else {
          newSelectedCategories.push(category)
        }

        // If all categories except "全部" are selected, select "全部" instead
        if (newSelectedCategories.length === this.categories.length - 1) {
          newSelectedCategories = ['全部']
        }
      }

      this.selectedCategories = newSelectedCategories
    },
    resetFilters() {
      this.timeRange = {
        start: '2023-01-01',
        end: '2023-12-31'
      }
      this.selectedCategories = ['全部']
    },
    applyFilters() {
      this.updateTimeRange(this.timeRange)
      this.updateSelectedCategories(this.selectedCategories)
    }
  }
}
</script>

<style scoped>
.right-panel {
  width: 25%;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.panel-section {
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 255, 204, 0.3);
  border-radius: 4px;
  padding: 10px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 16px;
  color: #00FFCC;
}

.section-icon {
  margin-right: 5px;
  font-style: normal;
}

.resource-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.resource-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  padding: 5px;
  border-radius: 4px;
}

.resource-item.active {
  background-color: rgba(0, 255, 204, 0.2);
  border: 1px solid #00FFCC;
}

.resource-image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  margin-bottom: 5px;
  border: 2px solid transparent;
  transition: all 0.3s;
}

.resource-item.active .resource-image {
  border-color: #00FFCC;
}

.resource-name {
  font-size: 12px;
  text-align: center;
}

.filter-section {
  margin-bottom: 15px;
}

.filter-label {
  font-size: 14px;
  margin-bottom: 5px;
  color: #00FFCC;
}

.time-range-picker {
  display: flex;
  align-items: center;
}

.time-input {
  flex: 1;
}

.time-input input {
  width: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 255, 204, 0.3);
  color: #fff;
  padding: 5px;
  border-radius: 4px;
}

.time-separator {
  margin: 0 5px;
}

.category-filter {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.category-item {
  padding: 5px 10px;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 255, 204, 0.3);
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.category-item.active {
  background-color: rgba(0, 255, 204, 0.2);
  border-color: #00FFCC;
  color: #00FFCC;
}

.filter-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.filter-btn {
  padding: 5px 15px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
}

.reset-btn {
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #fff;
}

.apply-btn {
  background-color: rgba(0, 255, 204, 0.2);
  border: 1px solid #00FFCC;
  color: #00FFCC;
}

.legend-explanation {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
}

.legend-color {
  width: 20px;
  height: 10px;
  margin-right: 10px;
}

.legend-desc {
  font-size: 12px;
}
</style>
