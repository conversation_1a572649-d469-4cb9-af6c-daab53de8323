# Standalone World Map Component

A reusable Vue 2 component for displaying interactive world maps with animated curved path trajectories. Features a dark tech aesthetic with customizable styling, fixed scale (no zoom), and white glowing path lines.

## Features

- 🗺️ **Leaflet-based world map** with custom styling
- 🎨 **Dark tech aesthetic** with configurable colors
- ✨ **Animated curved path trajectories** with white glowing lines
- 🔒 **Fixed scale** (no zoom/pan interactions)
- 🎯 **Self-contained** - no external dependencies on Vuex or other state management
- 🎛️ **Highly configurable** with comprehensive props
- 📱 **Responsive** design with customizable dimensions
- 🔧 **Easy integration** into any Vue 2 project

## Installation

### 1. Install Dependencies

```bash
npm install leaflet@^1.9.4
```

### 2. Copy Component Files

Copy the following files to your Vue project:

```
src/components/StandaloneWorldMap.vue
```

## Basic Usage

```vue
<template>
  <div>
    <standalone-world-map
      :path-data="pathData"
      :height="500"
      title="Global Distribution"
      @map-ready="onMapReady"
    />
  </div>
</template>

<script>
import StandaloneWorldMap from "./components/StandaloneWorldMap.vue";

export default {
  components: {
    StandaloneWorldMap,
  },
  data() {
    return {
      pathData: [
        {
          coords: [
            [40.7128, -74.006],
            [51.5074, -0.1278],
          ], // [lat, lng] format
          value: 150,
          name: "New York to London",
        },
        {
          coords: [
            [35.6762, 139.6503],
            [37.7749, -122.4194],
          ],
          value: 200,
          name: "Tokyo to San Francisco",
        },
      ],
    };
  },
  methods: {
    onMapReady(map) {
      console.log("Map is ready:", map);
      // Access the Leaflet map instance if needed
    },
  },
};
</script>
```

## Props Documentation

### Map Configuration

| Prop        | Type          | Default                 | Description                                |
| ----------- | ------------- | ----------------------- | ------------------------------------------ |
| `width`     | String/Number | `'100%'`                | Map container width (CSS units or pixels)  |
| `height`    | String/Number | `'500px'`               | Map container height (CSS units or pixels) |
| `title`     | String        | `'Global Distribution'` | Map title text                             |
| `showTitle` | Boolean       | `true`                  | Whether to display the title bar           |

### Map Behavior

| Prop     | Type   | Default   | Description                                 |
| -------- | ------ | --------- | ------------------------------------------- |
| `center` | Array  | `[20, 0]` | Initial map center coordinates `[lat, lng]` |
| `zoom`   | Number | `2`       | Initial zoom level (1-18)                   |

### Data

| Prop       | Type  | Default | Description                                  |
| ---------- | ----- | ------- | -------------------------------------------- |
| `pathData` | Array | `[]`    | Array of path objects (see Path Data Format) |

### Styling

| Prop       | Type    | Default           | Description                           |
| ---------- | ------- | ----------------- | ------------------------------------- |
| `colors`   | Object  | See Colors Object | Color configuration for map elements  |
| `showGrid` | Boolean | `true`            | Whether to show the tech grid overlay |

### Animation

| Prop               | Type    | Default | Description                        |
| ------------------ | ------- | ------- | ---------------------------------- |
| `animationEnabled` | Boolean | `true`  | Enable/disable marker animations   |
| `animationSpeed`   | Number  | `2000`  | Animation duration in milliseconds |

## Path Data Format

Each path object in the `pathData` array should have the following structure:

```javascript
{
  coords: [[startLat, startLng], [endLat, endLng]], // Required: Start and end coordinates
  value: 150,                                       // Optional: Numeric value (affects marker size)
  name: 'Source to Destination'                     // Optional: Path name (shown in tooltips)
}
```

### Example Path Data

```javascript
const pathData = [
  {
    coords: [
      [40.7128, -74.006],
      [51.5074, -0.1278],
    ], // New York to London
    value: 150,
    name: "New York to London",
  },
  {
    coords: [
      [35.6762, 139.6503],
      [37.7749, -122.4194],
    ], // Tokyo to San Francisco
    value: 200,
    name: "Tokyo to San Francisco",
  },
  {
    coords: [
      [55.7558, 37.6173],
      [28.7041, 77.1025],
    ], // Moscow to Delhi
    value: 120,
    name: "Moscow to Delhi",
  },
];
```

## Colors Object

The default color configuration follows the established dark tech aesthetic:

```javascript
const defaultColors = {
  land: "rgb(30, 65, 51)", // Land/country fill color
  borders: "rgb(76, 159, 123)", // Country border color
  ocean: "rgb(18, 46, 44)", // Ocean/background color
  pathLine: "#ffffff", // Path line color
  sourcePoint: "#ffffff", // Source marker color
  targetPoint: "#ffffff", // Target marker color
};
```

### Custom Colors Example

```vue
<standalone-world-map
  :path-data="pathData"
  :colors="{
    land: '#2a4d3a',
    borders: '#4c9f7b',
    ocean: '#122e2c',
    pathLine: '#00ffcc',
    sourcePoint: '#ff6b6b',
    targetPoint: '#4ecdc4',
  }"
/>
```

## Events

| Event       | Payload              | Description                               |
| ----------- | -------------------- | ----------------------------------------- |
| `map-ready` | Leaflet Map Instance | Emitted when the map is fully initialized |

## Methods

The component exposes several methods for programmatic control:

```javascript
// Access the component via ref
this.$refs.worldMap.addPath(newPathData);
this.$refs.worldMap.removePath(index);
this.$refs.worldMap.clearPaths();
this.$refs.worldMap.updateColors(newColors);
```

### Method Details

| Method                 | Parameters                  | Description                    |
| ---------------------- | --------------------------- | ------------------------------ |
| `addPath(pathData)`    | `pathData`: Object or Array | Add new path(s) to the map     |
| `removePath(index)`    | `index`: Number             | Remove path at specified index |
| `clearPaths()`         | None                        | Remove all paths from the map  |
| `updateColors(colors)` | `colors`: Object            | Update map colors dynamically  |

## Advanced Examples

### Dynamic Path Management

```vue
<template>
  <div>
    <button @click="addRandomPath">Add Random Path</button>
    <button @click="clearAllPaths">Clear All</button>

    <standalone-world-map
      ref="worldMap"
      :path-data="pathData"
      :height="500"
      @map-ready="onMapReady"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      pathData: [],
      cities: [
        { name: "New York", coords: [40.7128, -74.006] },
        { name: "London", coords: [51.5074, -0.1278] },
        { name: "Tokyo", coords: [35.6762, 139.6503] },
        { name: "Sydney", coords: [-33.8688, 151.2093] },
      ],
    };
  },
  methods: {
    addRandomPath() {
      const source =
        this.cities[Math.floor(Math.random() * this.cities.length)];
      const target =
        this.cities[Math.floor(Math.random() * this.cities.length)];

      if (source !== target) {
        const newPath = {
          coords: [source.coords, target.coords],
          value: Math.floor(Math.random() * 200) + 50,
          name: `${source.name} to ${target.name}`,
        };
        this.$refs.worldMap.addPath(newPath);
      }
    },

    clearAllPaths() {
      this.$refs.worldMap.clearPaths();
    },

    onMapReady(map) {
      console.log("Map ready with", this.pathData.length, "paths");
    },
  },
};
</script>
```

## Integration Guide

### Step 1: Project Setup

1. **Install Leaflet dependency:**

   ```bash
   npm install leaflet@^1.9.4
   ```

2. **Copy the component file:**
   Copy `StandaloneWorldMap.vue` to your `src/components/` directory.

3. **Import and register the component:**

   ```javascript
   import StandaloneWorldMap from "@/components/StandaloneWorldMap.vue";

   export default {
     components: {
       StandaloneWorldMap,
     },
   };
   ```

### Step 2: Basic Implementation

```vue
<template>
  <standalone-world-map
    :path-data="yourPathData"
    :height="400"
    title="Your Map Title"
  />
</template>
```

### Step 3: Data Preparation

Prepare your path data in the correct format:

```javascript
// Convert your data to the required format
const pathData = yourRawData.map((item) => ({
  coords: [
    [item.sourceLat, item.sourceLng],
    [item.targetLat, item.targetLng],
  ],
  value: item.volume || 100,
  name: `${item.source} to ${item.target}`,
}));
```

## Troubleshooting

### Common Issues

1. **Map not displaying:**

   - Ensure Leaflet is properly installed
   - Check that the container has a defined height
   - Verify path data format is correct

2. **Paths not showing:**

   - Check coordinate format: `[latitude, longitude]`
   - Ensure coordinates are valid numbers
   - Verify path data is not empty

3. **Styling issues:**

   - CSS conflicts may override component styles
   - Use browser dev tools to inspect applied styles
   - Ensure color values are valid CSS colors

4. **Performance with many paths:**
   - Consider limiting the number of simultaneous paths
   - Use the `clearPaths()` method before adding new data
   - Disable animations for better performance with many paths

### Browser Compatibility

- **Supported browsers:** Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **Mobile support:** iOS Safari 12+, Chrome Mobile 60+
- **IE support:** Not supported (requires modern JavaScript features)

## Technical Details

### Dependencies

- **Vue 2.6+** (peer dependency)
- **Leaflet 1.9.4** (automatically loaded via CDN if not available)

### Bundle Size

- **Component size:** ~15KB (minified)
- **Leaflet dependency:** ~140KB (loaded dynamically)

### Performance Considerations

- **Path rendering:** Optimized for up to 50 simultaneous paths
- **Animation:** Uses requestAnimationFrame for smooth performance
- **Memory management:** Automatic cleanup on component destruction

## License

This component is part of the 3D Path Map project. Please refer to the main project license for usage terms.
