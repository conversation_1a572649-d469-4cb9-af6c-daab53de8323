<template>
  <div class="standalone-map-test">
    <header class="test-header">
      <h1>Standalone World Map Test</h1>
      <p>Testing the standalone world map component without Vuex dependencies</p>
    </header>
    
    <div class="test-content">
      <div class="controls-section">
        <h2>Controls</h2>
        <div class="control-buttons">
          <button @click="loadTestData" class="btn btn-primary">Load Test Data</button>
          <button @click="clearData" class="btn btn-secondary">Clear Data</button>
          <button @click="toggleAnimation" class="btn btn-secondary">
            {{ animationEnabled ? 'Disable' : 'Enable' }} Animation
          </button>
          <button @click="addRandomPath" class="btn btn-accent">Add Random Path</button>
        </div>
        
        <div class="status-info">
          <p>Paths loaded: {{ pathData.length }}</p>
          <p>Animation: {{ animationEnabled ? 'Enabled' : 'Disabled' }}</p>
          <p>Map ready: {{ mapReady ? 'Yes' : 'No' }}</p>
        </div>
      </div>
      
      <div class="map-section">
        <h2>Standalone World Map Component</h2>
        <standalone-world-map
          ref="worldMap"
          :path-data="pathData"
          :height="500"
          :animation-enabled="animationEnabled"
          :colors="mapColors"
          title="Test World Map"
          @map-ready="onMapReady"
        />
      </div>
      
      <div class="comparison-section">
        <h2>Component Features Test</h2>
        <div class="feature-grid">
          <div class="feature-item">
            <h3>✅ Self-contained</h3>
            <p>No Vuex dependencies, works independently</p>
          </div>
          <div class="feature-item">
            <h3>✅ Dynamic Leaflet Loading</h3>
            <p>Automatically loads Leaflet from CDN</p>
          </div>
          <div class="feature-item">
            <h3>✅ Curved Path Trajectories</h3>
            <p>Smooth bezier curves between points</p>
          </div>
          <div class="feature-item">
            <h3>✅ Fixed Scale</h3>
            <p>No zoom/pan interactions enabled</p>
          </div>
          <div class="feature-item">
            <h3>✅ Dark Tech Aesthetic</h3>
            <p>Established color scheme maintained</p>
          </div>
          <div class="feature-item">
            <h3>✅ Animated Markers</h3>
            <p>White glowing animated markers</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import StandaloneWorldMap from '@/components/StandaloneWorldMap.vue'

export default {
  name: 'StandaloneMapTest',
  components: {
    StandaloneWorldMap
  },
  data() {
    return {
      pathData: [],
      animationEnabled: true,
      mapReady: false,
      mapColors: {
        land: 'rgb(30, 65, 51)',
        borders: 'rgb(76, 159, 123)',
        ocean: 'rgb(18, 46, 44)',
        pathLine: '#ffffff',
        sourcePoint: '#ffffff',
        targetPoint: '#ffffff'
      },
      testCities: [
        { name: 'Beijing', coords: [39.9042, 116.4074] },
        { name: 'New York', coords: [40.7128, -74.0060] },
        { name: 'London', coords: [51.5074, -0.1278] },
        { name: 'Tokyo', coords: [35.6762, 139.6503] },
        { name: 'Sydney', coords: [-33.8688, 151.2093] },
        { name: 'Moscow', coords: [55.7558, 37.6173] },
        { name: 'Delhi', coords: [28.7041, 77.1025] },
        { name: 'Singapore', coords: [1.3521, 103.8198] },
        { name: 'Paris', coords: [48.8566, 2.3522] },
        { name: 'Los Angeles', coords: [34.0522, -118.2437] }
      ]
    }
  },
  methods: {
    loadTestData() {
      this.pathData = [
        {
          coords: [[39.9042, 116.4074], [40.7128, -74.0060]], // Beijing to New York
          value: 200,
          name: 'Beijing to New York'
        },
        {
          coords: [[51.5074, -0.1278], [35.6762, 139.6503]], // London to Tokyo
          value: 180,
          name: 'London to Tokyo'
        },
        {
          coords: [[-33.8688, 151.2093], [1.3521, 103.8198]], // Sydney to Singapore
          value: 150,
          name: 'Sydney to Singapore'
        },
        {
          coords: [[55.7558, 37.6173], [28.7041, 77.1025]], // Moscow to Delhi
          value: 120,
          name: 'Moscow to Delhi'
        },
        {
          coords: [[48.8566, 2.3522], [34.0522, -118.2437]], // Paris to Los Angeles
          value: 160,
          name: 'Paris to Los Angeles'
        }
      ]
    },
    
    clearData() {
      this.pathData = []
    },
    
    toggleAnimation() {
      this.animationEnabled = !this.animationEnabled
    },
    
    addRandomPath() {
      const source = this.testCities[Math.floor(Math.random() * this.testCities.length)]
      const target = this.testCities[Math.floor(Math.random() * this.testCities.length)]
      
      if (source !== target) {
        const newPath = {
          coords: [source.coords, target.coords],
          value: Math.floor(Math.random() * 200) + 50,
          name: `${source.name} to ${target.name}`
        }
        this.pathData.push(newPath)
      }
    },
    
    onMapReady(map) {
      this.mapReady = true
      console.log('Standalone map is ready:', map)
      console.log('Map has zoom controls:', map.zoomControl)
      console.log('Map dragging enabled:', map.dragging.enabled())
    }
  },
  
  mounted() {
    console.log('StandaloneMapTest component mounted')
    // Load test data after a short delay to demonstrate dynamic loading
    setTimeout(() => {
      this.loadTestData()
    }, 1000)
  }
}
</script>

<style scoped>
.standalone-map-test {
  padding: 20px;
  background-color: #0F2E2C;
  color: #fff;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-header h1 {
  color: #00FFCC;
  font-size: 32px;
  margin-bottom: 10px;
}

.test-header p {
  color: #ccc;
  font-size: 16px;
}

.test-content {
  max-width: 1400px;
  margin: 0 auto;
}

.controls-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(0, 255, 204, 0.2);
}

.controls-section h2 {
  color: #00FFCC;
  margin-bottom: 15px;
}

.control-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #00FFCC;
  color: #0F2E2C;
}

.btn-primary:hover {
  background-color: #00E6B8;
}

.btn-secondary {
  background-color: rgba(0, 255, 204, 0.1);
  color: #00FFCC;
  border: 1px solid #00FFCC;
}

.btn-secondary:hover {
  background-color: rgba(0, 255, 204, 0.2);
}

.btn-accent {
  background-color: rgba(255, 107, 107, 0.1);
  color: #ff6b6b;
  border: 1px solid #ff6b6b;
}

.btn-accent:hover {
  background-color: rgba(255, 107, 107, 0.2);
}

.status-info {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.status-info p {
  margin: 0;
  padding: 5px 10px;
  background-color: rgba(0, 255, 204, 0.1);
  border-radius: 4px;
  font-size: 14px;
}

.map-section {
  margin-bottom: 30px;
}

.map-section h2 {
  color: #00FFCC;
  margin-bottom: 15px;
}

.comparison-section {
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(0, 255, 204, 0.2);
}

.comparison-section h2 {
  color: #00FFCC;
  margin-bottom: 20px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.feature-item {
  padding: 15px;
  background-color: rgba(0, 255, 204, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(0, 255, 204, 0.1);
}

.feature-item h3 {
  color: #00FFCC;
  margin-bottom: 8px;
  font-size: 16px;
}

.feature-item p {
  color: #ccc;
  margin: 0;
  font-size: 14px;
}
</style>
