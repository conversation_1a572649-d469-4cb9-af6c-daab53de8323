<template>
  <div class="global-distribution">
    <header class="page-header">
      <h1>全球分布</h1>
    </header>
    <div class="page-content">
      <leaflet-world-map />
    </div>
  </div>
</template>

<script>
import LeafletWorldMap from '@/components/LeafletWorldMap.vue'

export default {
  name: 'GlobalDistribution',
  components: {
    LeafletWorldMap
  }
}
</script>

<style scoped>
.global-distribution {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #0F2E2C;
  color: #fff;
}

.page-header {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  background-color: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(0, 255, 204, 0.3);
}

.page-header h1 {
  font-size: 24px;
  font-weight: 500;
  color: #00FFCC;
}

.page-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}
</style>
