<template>
  <div class="bottom-nav">
    <div v-for="(item, index) in navItems" :key="index" class="nav-item" :class="{ active: item.active }" @click="setActiveNav(index)">
      <div class="nav-icon" v-html="item.icon"></div>
      <div class="nav-label">{{ item.label }}</div>
    </div>
    <div class="pagination">
      <div class="page-info">共 {{ totalPages }} 页</div>
      <div class="page-controls">
        <div class="page-btn" @click="prevPage" :class="{ disabled: currentPage === 1 }">
          <span>&lt;</span>
        </div>
        <div v-for="page in visiblePages" :key="page" class="page-number" :class="{ active: page === currentPage }" @click="goToPage(page)">
          {{ page }}
        </div>
        <div class="page-btn" @click="nextPage" :class="{ disabled: currentPage === totalPages }">
          <span>&gt;</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BottomNav',
  data() {
    return {
      navItems: [
        {
          icon: '<svg viewBox="0 0 24 24" width="24" height="24"><path fill="currentColor" d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/></svg>',
          label: '数据总览',
          active: true
        },
        {
          icon: '<svg viewBox="0 0 24 24" width="24" height="24"><path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/></svg>',
          label: '全球分布',
          active: false
        },
        {
          icon: '<svg viewBox="0 0 24 24" width="24" height="24"><path fill="currentColor" d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/></svg>',
          label: '数据分析',
          active: false
        },
        {
          icon: '<svg viewBox="0 0 24 24" width="24" height="24"><path fill="currentColor" d="M12 11.55C9.64 9.35 6.48 8 3 8v11c3.48 0 6.64 1.35 9 3.55 2.36-2.19 5.52-3.55 9-3.55V8c-3.48 0-6.64 1.35-9 3.55zM12 8c1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3 1.34 3 3 3z"/></svg>',
          label: '资源目录',
          active: false
        },
        {
          icon: '<svg viewBox="0 0 24 24" width="24" height="24"><path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/></svg>',
          label: '系统信息',
          active: false
        }
      ],
      currentPage: 1,
      totalPages: 6,
      maxVisiblePages: 5
    }
  },
  computed: {
    visiblePages() {
      let startPage = Math.max(1, this.currentPage - Math.floor(this.maxVisiblePages / 2))
      let endPage = Math.min(this.totalPages, startPage + this.maxVisiblePages - 1)
      
      // Adjust start page if we're near the end
      if (endPage - startPage + 1 < this.maxVisiblePages) {
        startPage = Math.max(1, endPage - this.maxVisiblePages + 1)
      }
      
      return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i)
    }
  },
  methods: {
    setActiveNav(index) {
      this.navItems = this.navItems.map((item, i) => ({
        ...item,
        active: i === index
      }))
    },
    prevPage() {
      if (this.currentPage > 1) {
        this.currentPage--
      }
    },
    nextPage() {
      if (this.currentPage < this.totalPages) {
        this.currentPage++
      }
    },
    goToPage(page) {
      this.currentPage = page
    }
  }
}
</script>

<style scoped>
.bottom-nav {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  background-color: rgba(0, 0, 0, 0.3);
  border-top: 1px solid rgba(0, 255, 204, 0.3);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 5px 15px;
  border-radius: 4px;
  transition: all 0.3s;
}

.nav-item.active {
  background-color: rgba(0, 255, 204, 0.2);
}

.nav-icon {
  margin-bottom: 5px;
  color: #fff;
  transition: all 0.3s;
}

.nav-item.active .nav-icon {
  color: #00FFCC;
}

.nav-label {
  font-size: 12px;
  transition: all 0.3s;
}

.nav-item.active .nav-label {
  color: #00FFCC;
}

.pagination {
  display: flex;
  align-items: center;
}

.page-info {
  margin-right: 10px;
  font-size: 12px;
  color: #aaa;
}

.page-controls {
  display: flex;
  align-items: center;
}

.page-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 255, 204, 0.3);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  margin: 0 2px;
}

.page-btn:hover:not(.disabled) {
  background-color: rgba(0, 255, 204, 0.2);
  border-color: #00FFCC;
}

.page-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-number {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 255, 204, 0.3);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  margin: 0 2px;
  font-size: 12px;
}

.page-number.active {
  background-color: rgba(0, 255, 204, 0.2);
  border-color: #00FFCC;
  color: #00FFCC;
}

.page-number:hover:not(.active) {
  background-color: rgba(0, 255, 204, 0.1);
}
</style>
