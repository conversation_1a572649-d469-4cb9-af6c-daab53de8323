<template>
  <div class="left-panel">
    <div class="panel-section">
      <div class="section-header">
        <i class="section-icon">◉</i>
        <span>种质资源数据统计</span>
      </div>
      <div class="ring-chart-container">
        <div ref="ringChart" class="ring-chart"></div>
        <div class="ring-center-text">
          <div class="total-number">{{ ringChartData.totalTransactions }}</div>
          <div class="total-label">总数</div>
        </div>
      </div>
      <div class="legend-container">
        <div v-for="(item, index) in ringChartData.categories" :key="index" class="legend-item">
          <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
          <div class="legend-name">{{ item.name }}</div>
          <div class="legend-value">{{ item.value }}</div>
        </div>
      </div>
    </div>

    <div class="panel-section">
      <div class="section-header">
        <i class="section-icon">◉</i>
        <span>种类分布</span>
      </div>
      <div ref="barChart" class="bar-chart"></div>
    </div>

    <div class="panel-section">
      <div class="section-header">
        <i class="section-icon">◉</i>
        <span>区域占比</span>
      </div>
      <div ref="pieChart" class="pie-chart"></div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import * as echarts from 'echarts'

export default {
  name: 'LeftPanel',
  computed: {
    ...mapGetters('chartData', [
      'getRingChartData',
      'getBarChartData',
      'getPieChartData'
    ]),
    ringChartData() {
      return this.getRingChartData
    },
    barChartData() {
      return this.getBarChartData
    },
    pieChartData() {
      return this.getPieChartData
    }
  },
  mounted() {
    this.initRingChart()
    this.initBarChart()
    this.initPieChart()
    window.addEventListener('resize', this.resizeCharts)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeCharts)
    if (this.ringChart) this.ringChart.dispose()
    if (this.barChart) this.barChart.dispose()
    if (this.pieChart) this.pieChart.dispose()
  },
  methods: {
    initRingChart() {
      this.ringChart = echarts.init(this.$refs.ringChart)

      const option = {
        backgroundColor: 'transparent',
        color: this.ringChartData.categories.map(item => item.color),
        series: [
          {
            name: '种质资源分类',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false
              }
            },
            labelLine: {
              show: false
            },
            data: this.ringChartData.categories.map(item => ({
              value: item.value,
              name: item.name
            }))
          }
        ]
      }

      this.ringChart.setOption(option)
    },
    initBarChart() {
      this.barChart = echarts.init(this.$refs.barChart)

      const option = {
        backgroundColor: 'transparent',
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.barChartData.xAxis,
          axisLine: {
            lineStyle: {
              color: '#00FFCC'
            }
          },
          axisLabel: {
            color: '#fff'
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: '#00FFCC'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(0, 255, 204, 0.1)'
            }
          },
          axisLabel: {
            color: '#fff'
          }
        },
        series: this.barChartData.series.map(item => ({
          name: item.name,
          type: 'bar',
          data: item.data,
          stack: 'total',
          emphasis: {
            focus: 'series'
          },
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#0088FF' },
              { offset: 1, color: '#00FFAA' }
            ])
          }
        }))
      }

      this.barChart.setOption(option)
    },
    initPieChart() {
      this.pieChart = echarts.init(this.$refs.pieChart)

      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        color: ['#FF6B6B', '#FFD166', '#06D6A0', '#118AB2', '#073B4C'],
        series: [
          {
            name: '区域分布',
            type: 'pie',
            radius: '70%',
            center: ['50%', '50%'],
            data: this.pieChartData.data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              color: '#fff'
            }
          }
        ]
      }

      this.pieChart.setOption(option)
    },
    resizeCharts() {
      if (this.ringChart) this.ringChart.resize()
      if (this.barChart) this.barChart.resize()
      if (this.pieChart) this.pieChart.resize()
    }
  }
}
</script>

<style scoped>
.left-panel {
  width: 25%;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.panel-section {
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 255, 204, 0.3);
  border-radius: 4px;
  padding: 10px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 16px;
  color: #00FFCC;
}

.section-icon {
  margin-right: 5px;
  font-style: normal;
}

.ring-chart-container {
  position: relative;
  height: 150px;
}

.ring-chart {
  height: 100%;
  width: 100%;
}

.ring-center-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.total-number {
  font-size: 24px;
  font-weight: bold;
  color: #00FFCC;
}

.total-label {
  font-size: 12px;
  color: #fff;
}

.legend-container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  width: 50%;
  margin-bottom: 5px;
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
}

.legend-name {
  font-size: 12px;
  margin-right: 5px;
}

.legend-value {
  font-size: 12px;
  color: #00FFCC;
}

.bar-chart {
  height: 200px;
  width: 100%;
}

.pie-chart {
  height: 200px;
  width: 100%;
}
</style>
