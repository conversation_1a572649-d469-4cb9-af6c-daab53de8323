<template>
  <div class="world-map-demo">
    <header class="demo-header">
      <h1>Standalone World Map Component Demo</h1>
      <p>This demo showcases the reusable world map component with various configuration options.</p>
    </header>
    
    <div class="demo-content">
      <!-- Basic Usage Example -->
      <section class="demo-section">
        <h2>Basic Usage</h2>
        <div class="demo-controls">
          <button @click="loadSampleData" class="control-btn">Load Sample Data</button>
          <button @click="clearData" class="control-btn">Clear Data</button>
          <button @click="toggleAnimation" class="control-btn">
            {{ animationEnabled ? 'Disable' : 'Enable' }} Animation
          </button>
        </div>
        <standalone-world-map
          :path-data="pathData"
          :animation-enabled="animationEnabled"
          :height="400"
          title="Global Trade Routes"
          @map-ready="onMapReady"
        />
      </section>
      
      <!-- Custom Styling Example -->
      <section class="demo-section">
        <h2>Custom Styling</h2>
        <div class="demo-controls">
          <div class="color-controls">
            <label>
              Path Color:
              <input type="color" v-model="customColors.pathLine" @change="updateCustomColors">
            </label>
            <label>
              Land Color:
              <input type="color" v-model="customColors.land" @change="updateCustomColors">
            </label>
            <label>
              Ocean Color:
              <input type="color" v-model="customColors.ocean" @change="updateCustomColors">
            </label>
          </div>
        </div>
        <standalone-world-map
          :path-data="customPathData"
          :colors="customColors"
          :height="400"
          :show-grid="false"
          title="Custom Styled Map"
        />
      </section>
      
      <!-- Compact Example -->
      <section class="demo-section">
        <h2>Compact Version</h2>
        <standalone-world-map
          :path-data="compactPathData"
          :height="300"
          :show-title="false"
          :animation-enabled="false"
        />
      </section>
    </div>
    
    <!-- Code Examples -->
    <section class="code-examples">
      <h2>Code Examples</h2>
      
      <div class="code-block">
        <h3>Basic Implementation</h3>
        <pre><code>&lt;template&gt;
  &lt;standalone-world-map
    :path-data="pathData"
    :height="400"
    title="My World Map"
    @map-ready="onMapReady"
  /&gt;
&lt;/template&gt;

&lt;script&gt;
import StandaloneWorldMap from './StandaloneWorldMap.vue'

export default {
  components: {
    StandaloneWorldMap
  },
  data() {
    return {
      pathData: [
        {
          coords: [[40.7128, -74.0060], [51.5074, -0.1278]],
          value: 150,
          name: 'New York to London'
        }
      ]
    }
  },
  methods: {
    onMapReady(map) {
      console.log('Map is ready:', map)
    }
  }
}
&lt;/script&gt;</code></pre>
      </div>
      
      <div class="code-block">
        <h3>Custom Styling</h3>
        <pre><code>&lt;standalone-world-map
  :path-data="pathData"
  :colors="{
    land: '#2a4d3a',
    borders: '#4c9f7b',
    ocean: '#122e2c',
    pathLine: '#ffffff',
    sourcePoint: '#00ffcc',
    targetPoint: '#ff6b6b'
  }"
  :show-grid="true"
  :animation-enabled="true"
/&gt;</code></pre>
      </div>
    </section>
  </div>
</template>

<script>
import StandaloneWorldMap from './StandaloneWorldMap.vue'

export default {
  name: 'WorldMapDemo',
  components: {
    StandaloneWorldMap
  },
  data() {
    return {
      animationEnabled: true,
      pathData: [],
      customColors: {
        land: '#1e4133',
        borders: '#4c9f7b',
        ocean: '#122e2c',
        pathLine: '#ffffff',
        sourcePoint: '#ffffff',
        targetPoint: '#ffffff'
      },
      customPathData: [
        {
          coords: [[35.6762, 139.6503], [-33.8688, 151.2093]], // Tokyo to Sydney
          value: 200,
          name: 'Tokyo to Sydney'
        },
        {
          coords: [[48.8566, 2.3522], [-22.9068, -43.1729]], // Paris to Rio
          value: 180,
          name: 'Paris to Rio de Janeiro'
        }
      ],
      compactPathData: [
        {
          coords: [[40.7128, -74.0060], [51.5074, -0.1278]], // NYC to London
          value: 120,
          name: 'NYC to London'
        }
      ]
    }
  },
  methods: {
    loadSampleData() {
      this.pathData = [
        {
          coords: [[40.7128, -74.0060], [51.5074, -0.1278]], // New York to London
          value: 150,
          name: 'New York to London'
        },
        {
          coords: [[35.6762, 139.6503], [37.7749, -122.4194]], // Tokyo to San Francisco
          value: 200,
          name: 'Tokyo to San Francisco'
        },
        {
          coords: [[55.7558, 37.6173], [28.7041, 77.1025]], // Moscow to Delhi
          value: 120,
          name: 'Moscow to Delhi'
        },
        {
          coords: [[-33.8688, 151.2093], [1.3521, 103.8198]], // Sydney to Singapore
          value: 100,
          name: 'Sydney to Singapore'
        },
        {
          coords: [[48.8566, 2.3522], [40.4168, -3.7038]], // Paris to Madrid
          value: 80,
          name: 'Paris to Madrid'
        }
      ]
    },
    
    clearData() {
      this.pathData = []
    },
    
    toggleAnimation() {
      this.animationEnabled = !this.animationEnabled
    },
    
    updateCustomColors() {
      // Force reactivity update
      this.customColors = { ...this.customColors }
    },
    
    onMapReady(map) {
      console.log('Map is ready:', map)
    }
  },
  
  mounted() {
    // Load sample data on mount
    this.loadSampleData()
  }
}
</script>

<style scoped>
.world-map-demo {
  padding: 20px;
  background-color: #0F2E2C;
  color: #fff;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
}

.demo-header h1 {
  color: #00FFCC;
  font-size: 28px;
  margin-bottom: 10px;
}

.demo-header p {
  color: #ccc;
  font-size: 16px;
}

.demo-content {
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 40px;
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(0, 255, 204, 0.2);
}

.demo-section h2 {
  color: #00FFCC;
  margin-bottom: 20px;
  font-size: 20px;
}

.demo-controls {
  margin-bottom: 20px;
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  align-items: center;
}

.control-btn {
  padding: 8px 16px;
  background-color: rgba(0, 255, 204, 0.1);
  border: 1px solid #00FFCC;
  color: #00FFCC;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background-color: rgba(0, 255, 204, 0.2);
}

.color-controls {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.color-controls label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.color-controls input[type="color"] {
  width: 40px;
  height: 30px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.code-examples {
  max-width: 1200px;
  margin: 40px auto 0;
}

.code-examples h2 {
  color: #00FFCC;
  margin-bottom: 20px;
  font-size: 24px;
}

.code-block {
  margin-bottom: 30px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  overflow: hidden;
}

.code-block h3 {
  color: #00FFCC;
  padding: 15px 20px;
  margin: 0;
  background-color: rgba(0, 255, 204, 0.1);
  border-bottom: 1px solid rgba(0, 255, 204, 0.2);
}

.code-block pre {
  margin: 0;
  padding: 20px;
  overflow-x: auto;
}

.code-block code {
  color: #e6e6e6;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}
</style>
