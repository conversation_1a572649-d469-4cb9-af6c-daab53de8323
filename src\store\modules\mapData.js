// Store module for map-related data
export default {
  namespaced: true,
  state: {
    // Map configuration
    mapConfig: {
      backgroundColor: 'transparent',
      borderColor: '#00FFCC',
      borderWidth: 1,
      emphasis: {
        itemStyle: {
          areaColor: '#00FFCC',
          opacity: 0.3
        }
      }
    },
    // Path data for 3D trajectories
    pathData: [],
    // Selected regions
    selectedRegions: [],
    // Map zoom level
    zoomLevel: 1,
    // Map center position
    center: [0, 0]
  },
  mutations: {
    SET_PATH_DATA(state, data) {
      state.pathData = data;
    },
    SET_SELECTED_REGIONS(state, regions) {
      state.selectedRegions = regions;
    },
    SET_ZOOM_LEVEL(state, level) {
      state.zoomLevel = level;
    },
    SET_CENTER(state, center) {
      state.center = center;
    }
  },
  actions: {
    // Load path data from API or mock data
    loadPathData({ commit }) {
      // Mock data for now - in real app, this would be an API call
      const mockPaths = [
        // Asia routes
        {
          coords: [
            [116.4551, 40.2539], // Beijing
            [77.1025, 28.7041]   // Delhi
          ],
          value: 120,
          name: 'Beijing to Delhi'
        },
        {
          coords: [
            [116.4551, 40.2539], // Beijing
            [139.6917, 35.6895]  // Tokyo
          ],
          value: 180,
          name: 'Beijing to Tokyo'
        },
        {
          coords: [
            [121.4737, 31.2304], // Shanghai
            [103.8198, 1.3521]   // Singapore
          ],
          value: 150,
          name: 'Shanghai to Singapore'
        },
        {
          coords: [
            [77.1025, 28.7041],  // Delhi
            [67.0011, 24.8607]   // Karachi
          ],
          value: 90,
          name: 'Delhi to Karachi'
        },

        // Europe routes
        {
          coords: [
            [2.3522, 48.8566],   // Paris
            [13.4050, 52.5200]   // Berlin
          ],
          value: 110,
          name: 'Paris to Berlin'
        },
        {
          coords: [
            [-0.1278, 51.5074],  // London
            [2.3522, 48.8566]    // Paris
          ],
          value: 130,
          name: 'London to Paris'
        },
        {
          coords: [
            [9.1900, 45.4642],   // Milan
            [12.4964, 41.9028]   // Rome
          ],
          value: 80,
          name: 'Milan to Rome'
        },

        // Americas routes
        {
          coords: [
            [-74.0060, 40.7128], // New York
            [-118.2437, 34.0522] // Los Angeles
          ],
          value: 200,
          name: 'New York to Los Angeles'
        },
        {
          coords: [
            [-79.3832, 43.6532], // Toronto
            [-74.0060, 40.7128]  // New York
          ],
          value: 140,
          name: 'Toronto to New York'
        },
        {
          coords: [
            [-99.1332, 19.4326], // Mexico City
            [-77.0369, -12.0463] // Lima
          ],
          value: 110,
          name: 'Mexico City to Lima'
        },

        // Intercontinental routes
        {
          coords: [
            [116.4551, 40.2539], // Beijing
            [-74.0060, 40.7128]  // New York
          ],
          value: 250,
          name: 'Beijing to New York'
        },
        {
          coords: [
            [-0.1278, 51.5074],  // London
            [116.4551, 40.2539]  // Beijing
          ],
          value: 220,
          name: 'London to Beijing'
        },
        {
          coords: [
            [2.3522, 48.8566],   // Paris
            [-118.2437, 34.0522] // Los Angeles
          ],
          value: 190,
          name: 'Paris to Los Angeles'
        },
        {
          coords: [
            [139.6917, 35.6895], // Tokyo
            [-0.1278, 51.5074]   // London
          ],
          value: 230,
          name: 'Tokyo to London'
        },
        {
          coords: [
            [151.2093, -33.8688], // Sydney
            [37.6173, 55.7558]    // Moscow
          ],
          value: 210,
          name: 'Sydney to Moscow'
        }
      ];

      commit('SET_PATH_DATA', mockPaths);
    },
    // Update selected regions
    updateSelectedRegions({ commit }, regions) {
      commit('SET_SELECTED_REGIONS', regions);
    },
    // Update map zoom level
    updateZoomLevel({ commit }, level) {
      commit('SET_ZOOM_LEVEL', level);
    },
    // Update map center
    updateCenter({ commit }, center) {
      commit('SET_CENTER', center);
    }
  },
  getters: {
    getPathData: state => state.pathData,
    getSelectedRegions: state => state.selectedRegions,
    getZoomLevel: state => state.zoomLevel,
    getCenter: state => state.center,
    getMapConfig: state => state.mapConfig
  }
}
